/**
 * @fileoverview Form validation messages for email, password, and password reset functionality
 * <AUTHOR>
 * @version 1.0.0
 */

export const formValidationMessages = {
    email: {
        required: 'Email address is required',
        email: 'Please enter a valid email address',
    },
    password: {
        required: 'Password is required',
        pattern:
            'Atleast 8 characters, 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character',
    },
    confirmPassword: {
        required: 'Please confirm your password',
        passwordMismatch: 'Passwords do not match',
    },
} as const;

// Password Reset Messages
export const passwordResetMessages = {
    success: 'Your Password has been successfully reset.',
    failure: 'Password reset failed. Please try again.',
    invalidToken: 'Invalid reset token',
    invalidParameters: 'Invalid parameters',
    noCardData: 'No card data received',
} as const;
