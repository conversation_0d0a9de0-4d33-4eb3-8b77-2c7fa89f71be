@let profileData = profileData$ | async;
@if(profileData) {
<div class="w-full max-w-[32rem] mx-auto p-6 bg-white rounded-2xl shadow flex flex-col gap-6">
    <div class="text-base font-semibold text-zinc-950">
        Create a new password
    </div>
    <div class="text-sm font-normal text-neutral-500">
        Your new password must be different from the previous password.
    </div>

    <form [formGroup]="form" (ngSubmit)="onSubmit(profileData)" class="flex flex-col gap-4">
        <lib-text-input formControlName="password" label="New Password" type="password" placeholder="Enter new password"
            [required]="true" [showPasswordToggle]="true" [description]="fieldDescription.password"
            [errorMessages]="errorMessages.password" />

        <lib-text-input formControlName="confirmPassword" label="Confirm New Password" type="password"
            placeholder="Re-enter password" [required]="true" [showPasswordToggle]="true"
            [errorMessages]="errorMessages.confirmPassword" />

        <button [disabled]="form.invalid"
            class="w-full py-3 mt-2 text-base font-medium text-white bg-black-900 rounded-full transition disabled:opacity-60">
            Confirm Password
        </button>
    </form>

    <div class="text-center text-sm mt-2">
        Remembered it?
        <a routerLink="/auth/login" class="text-blue-600 underline">Login</a>
    </div>
</div>
} @else {
<div>Loading</div>
}