/**
 * @fileoverview Component for handling password reset functionality with token validation and form validation
 * <AUTHOR>
 * @version 1.0.0
 */

import { Component, inject, TemplateRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
    FormBuilder,
    FormGroup,
    Validators,
    ReactiveFormsModule,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
    TextInputComponent,
    FormValidatorService,
    ErrorMessage,
    FieldDescription,
} from 'lib-ui-kit';
import { STRONG_PASSWORD_REGEX } from '../registration-form-base/registration-form-base.component';
import { LibAuthPasswordResetBL } from '../../business-layer/lib-auth-password-reset-bl.service';
import { LibAuthPasswordResetDL } from '../../data-layer/lib-auth-password-reset-dl.service';
import { LibAuthValidateTokenDL } from '../../data-layer/lib-auth-validate-token-dl.service';
import { LibUserDetailDL } from '../../data-layer/lib-auth-user-details-dl.service';
import { catchError, EMPTY, map, Observable, switchMap } from 'rxjs';
import {
    formValidationMessages,
    passwordResetMessages,
} from 'lib/lib-app-core/src/lib/constants/form-validation-message';
import { UserLoginDTOModel } from '../../models/user-login-dto.model';
import {
    navigationUrls,
    navigationLabels,
} from 'lib/lib-app-core/src/lib/constants/navigation-constants';
import {
    ResetPasswordFormData,
    ResetPasswordQueryParams,
    TokenValidationResponse,
} from '../../interfaces/reset-password.interface';

@Component({
    selector: 'app-password-reset',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, TextInputComponent],
    providers: [
        LibAuthPasswordResetBL,
        LibAuthPasswordResetDL,
        LibAuthValidateTokenDL,
        LibUserDetailDL,
        FormValidatorService,
    ],
    templateUrl: './reset-password.component.html',
})
export class PasswordResetComponent {
    private readonly route = inject(ActivatedRoute);
    private readonly router = inject(Router);
    private readonly fb = inject(FormBuilder);
    private readonly passwordResetBL = inject(LibAuthPasswordResetBL);
    private readonly formValidatorService = inject(FormValidatorService);

    form: FormGroup = this.fb.group(
        {
            password: [
                '',
                [
                    Validators.required,
                    Validators.pattern(STRONG_PASSWORD_REGEX),
                ],
            ],
            confirmPassword: ['', Validators.required],
        },
        {
            validators: [
                this.formValidatorService.passwordMatchValidator(
                    'password',
                    'confirmPassword'
                ),
            ],
        }
    );

    /**
     * Error messages for the form fields
     *
     * This object contains the error messages for the form fields.
     * It can be used to display the error messages to the user or to handle the error messages
     * in the component.
     */
    errorMessages: ErrorMessage<ResetPasswordFormData> = {
        password: formValidationMessages.password,
        confirmPassword: formValidationMessages.confirmPassword,
    };

    /**
     * Field descriptions for the form fields
     *
     * This object contains the field descriptions for the form fields.
     * It can be used to display the field descriptions to the user or to handle the field descriptions
     * in the component.
     */
    fieldDescription: FieldDescription<ResetPasswordFormData> = {
        password: formValidationMessages.password.pattern,
    };

    /**
     *  Submit the form to reset the password
     * @param profileData - UserLoginDTOModel containing user details
     */
    onSubmit(profileData: UserLoginDTOModel): void {
        const { password } = this.form.value;

        this.passwordResetBL.resetPassword(password, profileData).subscribe({
            next: () => {
                this.redirectToSuccess();
            },
            error: () => {
                this.redirectToFailure();
            },
        });
    }

    /**
     * Observable that validates the reset token and fetches user details.
     * It uses the ActivatedRoute to get query parameters and passes them to the service.
     * The response is mapped to extract the ObjectGuid for further processing.
     *
     */
    profileData$ = this.route.queryParams.pipe(
        map((param) => this.validateParams(param)),
        switchMap((resetToken) =>
            this.passwordResetBL.validatePasswordToken(resetToken)
        ),
        map((response) => this.extractCardData(response)),
        switchMap((guid) => this.passwordResetBL.getUserDetails(guid)),
        catchError((error) => this.handleError(error.statusText))
    );

    /**
     * Validates the reset token from the query parameters.
     * If the token is invalid or missing, it redirects to the failure page with an error message.
     * @param params - ResetPasswordQueryParams containing the reset token
     * @returns The reset token
     * @throws Error if the reset token is invalid or missing
     */
    protected validateParams(params: ResetPasswordQueryParams): string {
        if (!params?.resetToken) {
            throw new Error(passwordResetMessages.failure);
        }
        return params.resetToken;
    }

    /**
     * Handles errors by navigating to the failure page with an error message.
     * @param error - The error to handle
     * @returns An empty observable to terminate the stream
     */
    private handleError(error: Error): Observable<never> {
        this.router.navigate([navigationUrls.failure], {
            queryParams: { message: error },
        });
        return EMPTY;
    }

    /**
     * Redirects to the success page with a success message.
     * @param message - The success message to display
     * @param redirectUrl - The URL to redirect to after success
     * @param redirectLabel - The label for the redirect link
     */
    private redirectToSuccess(): void {
        this.router.navigate([navigationUrls.success], {
            queryParams: {
                message: passwordResetMessages.success,
                redirectUrl: navigationUrls.login,
                redirectLabel: navigationLabels.continueToLogin,
            },
        });
    }
    /**
     * Redirects to the failure page with an error message.
     * @param message - The error message to display
     * @param redirectUrl - The URL to redirect to after failure
     * @param redirectLabel - The label for the redirect link
     * */
    private redirectToFailure(): void {
        this.router.navigate([navigationUrls.failure], {
            queryParams: {
                message: passwordResetMessages.failure,
                redirectUrl: navigationUrls.login,
                redirectLabel: navigationLabels.retryPasswordReset,
            },
        });
    }

    /**
     * Extracts the ObjectGuid from the response data.
     * If the ObjectGuid is not present, it throws an error with a specific message.
     * @param response - The response containing the ObjectGuid
     * @returns The ObjectGuid as a string
     * @throws Error if the ObjectGuid is missing
     */
    private extractCardData(response: TokenValidationResponse): string {
        if (!response?.data?.ObjectGuid) {
            throw new Error(passwordResetMessages.noCardData);
        }
        return response.data.ObjectGuid;
    }
}
