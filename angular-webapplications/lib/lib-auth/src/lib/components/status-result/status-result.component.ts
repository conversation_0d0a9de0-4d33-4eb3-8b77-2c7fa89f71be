import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map, Observable } from 'rxjs';

@Component({
    selector: 'lib-status-result',
    templateUrl: './status-result.component.html',
    styleUrls: ['./status-result.component.css'],
    imports: [CommonModule],
})
export class StatusResultComponent {
    private _route = inject(ActivatedRoute);
    private _router = inject(Router);
    status$: Observable<string | undefined>;
    queryParams$: Observable<{
        message?: string;
        redirectUrl?: string;
        redirectLabel?: string;
    } | null>;

    /**
     * Constructor for the StatusResultComponent
     * 
     * This constructor initializes the status$ and queryParams$ observables
     * by subscribing to the route data and query params respectively.
     */
    constructor() {
        this.status$ = this._route.data.pipe(map((data) => data['status']));
        this.queryParams$ = this._route.queryParams;
    }

    //This method is used to navigate to the specified URL
    navigateToUrl(redirectUrl?: string): void {
        if (redirectUrl) {
            this._router.navigate([redirectUrl]);
        }
    }
}
