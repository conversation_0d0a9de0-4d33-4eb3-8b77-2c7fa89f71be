@let status = status$ | async;
<div class="min-h-screen flex items-center justify-center p-4 bg-gray-50">
    <div class="w-full max-w-md p-8 text-center bg-white rounded-lg border border-blue-200 shadow-sm">
        <!-- Icon -->
        @if (status === 'success') {
        <div class="flex justify-center mb-6">
            <img src="assets/icons/success.svg" alt="Success" class="w-24 h-24 object-contain" />
        </div>
        <h1 class="text-2xl font-bold mb-4 text-green-600">Success</h1>
        } @else {
        <div class="flex justify-center mb-6">
            <img src="assets/icons/failure.svg" alt="Failure" class="w-24 h-24 object-contain" />
        </div>
        <h1 class="text-2xl font-bold mb-4 text-red-600">Failed</h1>
        }
        <!-- Message from query params -->
        @if (queryParams$ | async; as queryParams) {
        <p class="text-base mb-8 text-gray-800">
            {{ queryParams.message }}
        </p>

        <!-- Button -->
        @if (queryParams.redirectLabel) {
        <button
            class="w-full py-3 px-6 text-lg font-semibold text-white bg-black rounded-full transition-colors duration-200 hover:bg-gray-800"
            (click)="navigateToUrl(queryParams.redirectUrl)">
            {{ queryParams.redirectLabel }}
        </button>
        } }
    </div>
</div>