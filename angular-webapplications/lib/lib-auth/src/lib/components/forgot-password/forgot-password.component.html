@let errorMessage = forgotPasswordErrorMessage();
<div class="p-5 grid gap-5">
    <div class="flex flex-col items-center">
        <div class="mb-2">
            <img src="assets/icons/forgotten-password.svg" alt="Forgot Password" class="w-12 h-12" />
        </div>
        <h1 class="text-xl font-semibold text-center">Forgot password</h1>
    </div>

    <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()">
        <div class="flex flex-col gap-5">
            <div class="flex flex-col gap-1 items-center justify-center text-sm">
                <span class="font-medium">Identify yourself</span>
                <p class="w-full text-primary text-center">
                    Please enter the registered email address to receive link to reset
                </p>
            </div>

            <lib-text-input formControlName="email" label="Email Address" type="email" placeholder="Enter email address"
                [errorMessages]="errorMessages.email"></lib-text-input>

            <button [disabled]="resetPasswordForm.invalid"
                class="w-full py-3 px-4 text-white font-medium bg-primary rounded-4xl border-0 disabled:bg-surface">
                Submit
            </button>
        </div>
    </form>
    @if(errorMessage) {
        <div class="w-full text-center pl-2 mt-1 text-xs text-feedback-error">
           {{errorMessage}}
        </div>
    }@else if(forgotPasswordResponse()) {
        <div class="w-full text-center pl-2 mt-1 text-xs text-primary">
            An email has been sent to
            <strong>{{ resetPasswordForm.value.email  }}</strong>
        </div>
    }
</div>