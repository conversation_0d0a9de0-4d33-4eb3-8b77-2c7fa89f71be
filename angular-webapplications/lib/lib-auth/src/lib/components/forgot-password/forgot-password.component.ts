/**
 * @fileoverview Component for handling forgot password functionality with form validation
 * <AUTHOR>
 * @version 1.0.0
 */

import { CommonModule } from '@angular/common';
import { Component, inject, TemplateRef } from '@angular/core';
import {
    Form<PERSON>uilder,
    FormControl,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { ErrorMessage, TextInputComponent } from 'lib-ui-kit';
import { LibAuthForgotPasswordBL } from '../../business-layer/lib-auth-forgot-password-bl.service';
import { LibAuthForgotPasswordDL } from '../../data-layer/lib-auth-forgot-password-dl.service';
import { formValidationMessages } from 'lib/lib-app-core/src/lib/constants/form-validation-message';
interface ResetPasswordFormData {
    email: FormControl<string | null>;
}
@Component({
    selector: 'lib-forgot-password',
    standalone: true,
    imports: [ReactiveFormsModule, CommonModule, TextInputComponent],
    templateUrl: './forgot-password.component.html',
    providers: [LibAuthForgotPasswordBL, LibAuthForgotPasswordDL],
    styleUrl: './forgot-password.component.css',
})
export class ForgotPasswordComponent {
    resetPasswordForm: FormGroup;
    private _libAuthForgotPasswordBL: LibAuthForgotPasswordBL = inject(
        LibAuthForgotPasswordBL
    );

    /**
     * Signal for the forgot password response
     *
     * This signal contains the response from the forgot password process.
     * It can be used to display the response to the user or to handle the response
     * in the component.
     */
    forgotPasswordResponse = this._libAuthForgotPasswordBL.data;
    forgotPasswordErrorMessage = this._libAuthForgotPasswordBL.errorMessage;

    /**
     * Error messages for the form fields
     *
     * This object contains the error messages for the form fields.
     * It can be used to display the error messages to the user or to handle the error messages
     * in the component.
     */
    errorMessages: ErrorMessage<ResetPasswordFormData> = {
        email: formValidationMessages.email,
    };

    constructor(private fb: FormBuilder) {
        this.resetPasswordForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
        });

        /**
         * Subscribe to form value changes to clear success message when user starts typing
         */
        this.resetPasswordForm.valueChanges.subscribe(() => {
            // Clear success message when user modifies the email
            if (this.forgotPasswordResponse()) {
                this._libAuthForgotPasswordBL.data.set(null);
            }
            // Clear error message when user modifies the form
            if (this.forgotPasswordErrorMessage()) {
                this._libAuthForgotPasswordBL.errorMessage.set(null);
            }
        });
    }

    /**
     * Handles the form submission for the forgot password process
     *
     * This method validates the form and triggers the forgot password process
     * if the form is valid.
     */
    onSubmit() {
        this._libAuthForgotPasswordBL.forgotPassword(
            this.resetPasswordForm.value.email
        );
    }
}
