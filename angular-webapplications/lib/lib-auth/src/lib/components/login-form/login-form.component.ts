/**
 * @fileoverview Component for handling user login functionality with form validation and navigation
 * <AUTHOR>
 * @version 1.0.0
 */

import { CommonModule } from '@angular/common';
import {
    Component,
    TemplateRef,
    computed,
    effect,
    inject,
    signal,
} from '@angular/core';
import {
    Form<PERSON><PERSON>er,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { ForgotPasswordComponent } from '../forgot-password/forgot-password.component';
import {
    ErrorMessage,
    ModalComponent,
    PageFooterComponent,
    TextInputComponent,
} from 'lib-ui-kit';
import { STRONG_PASSWORD_REGEX } from '../registration-form-base/registration-form-base.component';
import { LibAuthLoginBL } from 'lib/lib-auth/src/lib/business-layer/lib-auth-login-bl.service';
import { LibAuthLoginDL } from '../../data-layer/lib-auth-login-dl.service';
import { Router } from '@angular/router';
import { RouterLink } from '@angular/router';
import { formValidationMessages } from 'lib/lib-app-core/src/lib/constants/form-validation-message';
import { LoginFormData } from '../../interfaces/login-form.interface';

export type ErrorMessagLogin<T> = {
    [K in keyof T]?: Record<string, string | TemplateRef<Component> | null>;
};

@Component({
    selector: 'lib-login-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ForgotPasswordComponent,
        TextInputComponent,
        ModalComponent,
        PageFooterComponent,
        RouterLink,
    ],
    templateUrl: './login-form.component.html',
    styleUrl: './login-form.component.css',
    providers: [LibAuthLoginBL, LibAuthLoginDL],
})
export class LoginFormComponent {
    readonly showForgotPassword = signal(false);
    readonly loginForm: FormGroup;
    private readonly _authBL = inject(LibAuthLoginBL);
    private readonly _router = inject(Router);
    readonly loading = computed(() => this._authBL.loading());
    readonly loginErrorMessage = this._authBL.errorMessage;

    errorMessages: ErrorMessage<LoginFormData> = {
        email: formValidationMessages.email,
        password: formValidationMessages.password,
    };

    constructor(private fb: FormBuilder) {
        this.loginForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
            password: [
                '',
                [
                    Validators.required,
                    Validators.pattern(STRONG_PASSWORD_REGEX),
                ],
            ],
        });

        /**
         * Subscribes to the form value changes and resets the login error state
         * on form change
         */
        this.loginForm.valueChanges.subscribe(() => {
            // Reset login error state on form change
            if (this.loginErrorMessage()) {
                this._authBL.errorMessage.set(null);
            }
        });

        /**
         * Subscribes to the login data and navigates to the my signed waivers page
         * if the login is successful.
         */
        effect(() => {
            const loginData = this._authBL.loginData();
            if (loginData) {
                this._router.navigate([`/waivers/my-signed-waivers`]);
            }
        });
    }

    /**
     * Handles the form submission for the login process
     * 
     * This method validates the form and triggers the login process
     * if the form is valid.
     */
    onSubmit() {
        const { email, password } = this.loginForm.value;
        this._authBL.login(email, password);
    }

    closeForgotPassword() {
        this.showForgotPassword.set(false);
    }
}
