import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { differenceInYears, isValid, parse } from 'date-fns';
import { CheckBoxComponent, DATE_FORMAT, DatePickerComponent, ErrorMessage, FieldDescription, PageFooterComponent, PhoneInputComponent, TextInputComponent } from 'lib-ui-kit';

interface RegisterFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
    email: FormControl<string | null>;
    mobileNumber: FormControl<string | null>;
    password: FormControl<string | null>;
    confirmPassword: FormControl<string | null>;
    optInPromotion: FormControl<boolean | null>;
}

interface MinorFormData {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
}
export const MINIMUM_USER_AGE = 18;

export const STRONG_PASSWORD_REGEX =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
@Component({
    selector: 'lib-registration-form-base',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TextInputComponent,
        DatePickerComponent,
        PhoneInputComponent,
        PageFooterComponent,
        CheckBoxComponent,
        RouterLink
    ],
    templateUrl: './registration-form-base.component.html',
    styleUrl: './registration-form-base.component.css'
})
export class RegistrationFormBaseComponent {

    registerForm: FormGroup;
    optInPromotion = false;
    protected readonly dateFormat = DATE_FORMAT;
    minors: MinorFormData[] = [];

    fieldDescription: FieldDescription<RegisterFormData> = {
        password:
            'Atleast 8 characters, 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character',
    };

    errorMessages: ErrorMessage<RegisterFormData> = {
        firstName: {
            required: 'First name is required',
        },
        lastName: {
            required: 'Last name is required',
        },
        dateOfBirth: {
            required: 'Date of birth is required',
            inValidDateOfBirth: 'Primary account holder must be above 18 years',
        },
        email: {
            required: 'Email address is required',
            email: 'Please enter a valid email address',
        },
        mobileNumber: {
            required: 'Mobile number is required',
        },
        password: {
            required: 'Password is required',
            pattern:
                'Atleast 8 characters, 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character',
        },
        confirmPassword: {
            required: 'Please confirm your password',
            passwordMismatch: 'Passwords do not match',
        },
    };

    constructor(private fb: FormBuilder, private router: Router) {
        this.registerForm = this.fb.group(
            {
                firstName: ['', Validators.required],
                lastName: ['', Validators.required],
                dateOfBirth: ['', Validators.required],
                email: ['', [Validators.required, Validators.email]],
                mobileNumber: ['', Validators.required],
                password: [
                    '',
                    [Validators.required, Validators.pattern(STRONG_PASSWORD_REGEX)],
                ],
                confirmPassword: ['', Validators.required],
                optInPromotion: [true],
            },
            {
                validators: [this.passwordMatchValidator, this.dobValidator],
            }
        );
    }

    passwordMatchValidator(form: FormGroup) {
        const password = form.get('password')?.value;
        const confirmPassword = form.get('confirmPassword')?.value;

        if (password !== confirmPassword) {
            form.get('confirmPassword')?.setErrors({ passwordMismatch: true });
            return { passwordMismatch: true };
        }

        return null;
    }

    dobValidator(form: FormGroup) {
        const dateOfBirth = form.get('dateOfBirth')?.value;

        if (!dateOfBirth) {
            form.get('dateOfBirth')?.setErrors({ required: true });
            return { required: true };
        }

        const parsedDate = parse(dateOfBirth, DATE_FORMAT, new Date());

        // Check if parsed date is valid
        if (!isValid(parsedDate)) {
            form.get('dateOfBirth')?.setErrors({ inValidDateOfBirth: true });
            return { inValidDateOfBirth: true };
        }
        const age = differenceInYears(new Date(), parsedDate);

        if (age < MINIMUM_USER_AGE) {
            form.get('dateOfBirth')?.setErrors({ inValidDateOfBirth: true });
            return { inValidDateOfBirth: true };
        } else {
            form.get('dateOfBirth')?.setErrors(null);
            return null;
        }
    }

    addMinor(): void {
        this.minors.push({
            firstName: '',
            lastName: '',
            dateOfBirth: '',
        });
    }

    removeMinor(index: number): void {
        this.minors.splice(index, 1);
    }

    onSubmit(): void {
        if (this.registerForm.valid) {
            // Add registration logic here
        } else {
            this.registerForm.markAllAsTouched();
        }

        //FIXME TO BE REMOVED AFTER DEMO 
        this.router.navigate(['/waivers/add-participants/53/1']);

    }

}
