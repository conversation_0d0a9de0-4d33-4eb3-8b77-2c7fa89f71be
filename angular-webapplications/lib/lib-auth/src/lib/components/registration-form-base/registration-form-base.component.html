<div>
    <p class="text-sm text-neutral-dark mb-6">
        Add the below details to get an account. We don't share or sell your data.
    </p>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <fieldset class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- First Name -->
            <lib-text-input id="firstName" formControlName="firstName" label="First Name" placeholder="Enter first name"
                [errorMessages]="errorMessages.firstName"></lib-text-input>

            <!-- Last Name -->
            <lib-text-input id="lastName" formControlName="lastName" label="Last Name" placeholder="Enter last name"
                [errorMessages]="errorMessages.lastName"></lib-text-input>

            <!-- Date of Birth -->
            <lib-date-picker id="dateOfBirth" formControlName="dateOfBirth" label="Date of Birth"
                [placeholder]="dateFormat.toUpperCase()" [format]="dateFormat" [rightOffset]="0"
                [errorMessages]="errorMessages.dateOfBirth" [rightOffset]="0" [minYear]="1900"></lib-date-picker>
        </fieldset>

        <fieldset class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Email Address -->
            <lib-text-input id="email" formControlName="email" label="Email Address" type="email"
                placeholder="Enter email address" [errorMessages]="errorMessages.email"></lib-text-input>

            <!-- Mobile Number -->
            <lib-phone-input id="mobileNumber" formControlName="mobileNumber" label="Mobile Number"
                placeholder="Enter mobile number" [errorMessages]="errorMessages.mobileNumber"></lib-phone-input>
        </fieldset>

        <fieldset class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Password -->
            <lib-text-input id="password" formControlName="password" label="Password" type="password"
                placeholder="Enter password" [showPasswordToggle]="true" [description]="fieldDescription.password"
                [errorMessages]="errorMessages.password"></lib-text-input>

            <!-- Confirm Password -->
            <lib-text-input id="confirmPassword" formControlName="confirmPassword" label="Confirm Password"
                type="password" placeholder="Re-enter password" [showPasswordToggle]="true"
                [errorMessages]="errorMessages.confirmPassword"></lib-text-input>
        </fieldset>

        <!-- Promotion Card -->
        <div class="mb-6 max-w-xs p-2 rounded-3xl bg-surface-lightest">
            <div class="relative overflow-hidden rounded-xl w-full">
                <img src="assets/images/opt-promotion.jpg" alt="Promotion" class="w-full h-36 object-cover scale-125" />
                <div class="absolute inset-0 flex items-end p-5 text-surface-white bg-gradient-to-top-black">
                    <h3 class="text-xl font-medium max-w-[80%]">
                        Get 50% off on your next party booking
                    </h3>
                </div>
            </div>

            <!-- Opt-in Checkbox -->
            <div class="flex items-center justify-between space-x-2 px-2 mt-2">
                <lib-checkbox id="optInPromotion" [name]="'optInPromotion'" [label]="'Opt in for promotion'"
                    [formControlName]="'optInPromotion'" customClass="w-[18px] h-[18px]"
                    [errorMessages]="errorMessages.optInPromotion" />
                <a href="#" class="text-sm text-blue-600 underline">View Benefits</a>
            </div>
        </div>

        <!-- Minors -->
        <ng-content select="[add-minor-form]"></ng-content>

        <!-- Register Button -->
        <hr class="my-4 text-surface hidden md:block" />

        <!-- Desktop CTA -->
        <div class="hidden md:block">
            <ng-container *ngTemplateOutlet="registrationCTA"></ng-container>
        </div>
    </form>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <ng-container *ngTemplateOutlet="registrationCTA"></ng-container>
</lib-page-footer>

<ng-template #registrationCTA>
    <div class="flex flex-col md:flex-row items-center gap-4">
        <!-- Login Button -->
        <button [disabled]="registerForm.invalid" (click)="onSubmit()" type="submit"
            class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface">
            Register and sign waiver
        </button>
        <p class="text-sm text-center md:text-right">
            Already have an account?
            <a routerLink="/auth/login" class="text-blue-600 underline">Login</a>
        </p>
    </div>
</ng-template>