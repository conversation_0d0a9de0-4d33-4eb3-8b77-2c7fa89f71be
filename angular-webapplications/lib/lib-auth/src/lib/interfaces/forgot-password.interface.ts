/**
 * @fileoverview Interfaces for forgot password functionality
 * <AUTHOR>
 * @version 1.0.0
 */
export interface ForgotPasswordAPIParams {
    Username: string;
}

export interface ContactDTO {
    ContactTypeId: number;
    ContactType: number;
    Attribute1: string;
}

export interface ProfileDTO {
    FirstName: string;
    Password: string;
    LastName: string;
    DateOfBirth: string;
    IsActive: boolean;
    IsChanged: boolean;
    IsChangedRecursive: boolean;
    Id: number;
    ContactDTOList: ContactDTO[];
}

export interface ResetPasswordParams {
    PhoneNumber: string;
    Password: string;
    SiteId: number;
    Nationality: string;
    ConfirmPassword: string;
    ContactDTOList: ContactDTO[];
    ProfileDTO: ProfileDTO;
    Verified: boolean;
    Email: string;
}
export interface UserDetailsParams {
    guid: string;
}
