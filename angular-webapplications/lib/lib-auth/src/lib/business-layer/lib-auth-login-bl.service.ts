/**
 * @fileoverview Business layer service for handling user login functionality
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable, signal } from '@angular/core';
import { LibAuthLoginDL } from '../data-layer/lib-auth-login-dl.service';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';
import { UserLoginDTOModel } from '../models/user-login-dto.model';

@Injectable()
export class LibAuthLoginBL extends RequestState {
    private _libAuthLoginDL = inject(LibAuthLoginDL);
    loginData = signal<UserLoginDTOModel | null>(null);
    private _cookieService = inject(CookieService);

    /**
     * Handles the login process by building API parameters,
     * invoking the Data Layer service, and managing the request state.
     *
     * @param userName - The username for login.
     * @param password - The password for login.
     */
    login(userName: string, password: string) {
        this._libAuthLoginDL.buildApiParams({
            UserName: userName,
            Password: password,
        });
        const login$ = this._libAuthLoginDL.load();
        this._handleRequest(login$, (response) => {
            this.loginData.set(UserLoginDTOModel.fromSingle(response.data));
            this._cookieService.setCookie('userId', response.data.Id);
        });
    }
}
