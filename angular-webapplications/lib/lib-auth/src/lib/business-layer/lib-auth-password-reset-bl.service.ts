/**
 * @fileoverview Business layer service for handling password reset functionality
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { LibAuthPasswordResetDL } from '../data-layer/lib-auth-password-reset-dl.service';

import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { LibAuthValidateTokenDL } from '../data-layer/lib-auth-validate-token-dl.service';
import { LibUserDetailDL } from '../data-layer/lib-auth-user-details-dl.service';
import { map } from 'rxjs';
import { ResetPasswordParams } from '../interfaces/forgot-password.interface';

@Injectable()
export class LibAuthPasswordResetBL extends RequestState {
    private _libAuthPasswordResetDL = inject(LibAuthPasswordResetDL);
    private _libAuthValidateTokenDL = inject(LibAuthValidateTokenDL);
    private _libGetUserDetailDL = inject(LibUserDetailDL);

    /**
     * Validates a password reset token
     *
     * This method sends a request to validate a password reset token.
     * The request is handled through the data layer service and the response is returned.
     *
     * @param token - The token to validate
     * @returns Observable<any> - The response from the data layer service
     */
    validatePasswordToken(token: string) {
        this._libAuthValidateTokenDL.buildApiParams({
            Token: token,
        });
        return this._libAuthValidateTokenDL.load();
    }

    /**
     * Resets a user's password
     *
     * This method sends a request to reset a user's password.
     * The request is handled through the data layer service and the response is returned.
     *
     * @param newPassword - The new password to set for the user
     * @param profileData - The profile data of the user
     * @returns Observable<any> - The response from the data layer service
     */
    resetPassword(newPassword: string, profileData: any) {
        const payload: ResetPasswordParams = {
            ...profileData,
            Password: newPassword,
            ProfileDTO: {
                ...profileData.ProfileDTO,
                Password: newPassword,
            },
        };

        this._libAuthPasswordResetDL.buildApiParams(payload);
        return this._libAuthPasswordResetDL.load();
    }

    /**
     * Gets user details by GUID
     *
     * This method sends a request to get user details by GUID.
     * The request is handled through the data layer service and the response is returned.
     *
     * @param guid - The GUID of the user to get details for
     * @returns Observable<any> - The response from the data layer service
     */
    getUserDetails(guid: string) {
        this._libGetUserDetailDL.buildApiParams({
            guid,
        });
        return this._libGetUserDetailDL
            .load()
            .pipe(map((data) => data?.data[0] || null));
    }
}
