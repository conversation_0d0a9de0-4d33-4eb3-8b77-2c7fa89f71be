/**
 * @fileoverview Business layer service for handling forgot password functionality
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable, signal } from '@angular/core';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { LibAuthForgotPasswordDL } from '../data-layer/lib-auth-forgot-password-dl.service';

@Injectable()
export class LibAuthForgotPasswordBL extends RequestState {
    private _libAuthForgotPasswordDL = inject(LibAuthForgotPasswordDL);
    data = signal<any>(null);


    /**
     * Initiates the forgot password process for a user
     * 
     * This method sends a request to reset the password for the specified email address.
     * The request is handled through the data layer service and the response is stored
     * in the component's data signal for further processing.
     * 
     * @param email - The email address of the user requesting password reset
     * @returns void - The method doesn't return a value, but updates the internal data signal
     * 
     */
    forgotPassword(email: string): void {
        this._libAuthForgotPasswordDL.buildApiParams({
            Username: email,
        });
        const forgotPassword$ = this._libAuthForgotPasswordDL.load();
        this._handleRequest(forgotPassword$, (data) => {
            this.data.set(data);
        });
    }
}
