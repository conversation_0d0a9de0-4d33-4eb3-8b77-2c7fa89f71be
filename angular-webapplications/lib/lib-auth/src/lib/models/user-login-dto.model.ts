/**
 * @fileoverview Model for user login
 * <AUTHOR>
 * @version 1.0.0
 */
import { BaseDTO } from 'lib/lib-app-core/src/lib/models/base-dto.model';
import { UserAddressDTOModel } from './user-address-dto.model';
import { UserContactDTOModel } from './user-contact-dto.model';
import { UserProfileDTOModel } from './user-profile-dto.model';
import { CustomDataSetDTOModel } from './custom-data-set-dto.model';

export class UserLoginDTOModel extends BaseDTO<UserLoginDTOModel> {
    Id!: number;
    IsActive!: boolean;
    IsBonusLoaded!: boolean;
    ProfileId!: number;
    MembershipId!: number;
    Title!: string;
    FirstName!: string;
    NickName!: string;
    MiddleName!: string;
    LastName!: string;
    ExternalSystemReference!: string;
    CustomerType!: number;
    UniqueIdentifier!: string;
    TaxCode!: string;
    DateOfBirth!: string;
    Gender!: string;
    Anniversary!: string | null;
    TeamUser!: boolean;
    RightHanded!: boolean;
    OptInPromotions!: boolean;
    OptInPromotionsMode!: string;
    PolicyTermsAccepted!: boolean;
    Company!: string;
    UserName!: string;
    PhotoURL!: string;
    IdProofFileURL!: string;
    LastLoginTime!: string | null;
    Designation!: string;
    CustomDataSetId!: number;
    Notes!: string;
    CardNumber!: string;
    Channel!: string;
    Verified!: boolean;
    Password!: string;
    PhoneNumber!: string;

    AddressDTOList: UserAddressDTOModel[] | null = null;
    ContactDTOList: UserContactDTOModel[] = [];
    ProfileDTO!: UserProfileDTOModel;
    CustomerVerificationDTO: any = null;
    CustomDataSetDTO!: CustomDataSetDTOModel;
    PhoneContactDTO!: UserContactDTOModel;
    LatestAddressDTO!: UserAddressDTOModel;

    CreatedBy!: string;
    CreationDate!: string;
    LastUpdatedBy!: string;
    LastUpdateDate!: string;
    SiteId!: number;
    MasterEntityId!: number;
    SynchStatus!: boolean;
    Guid!: string;

    constructor() {
        super();
    }
}
