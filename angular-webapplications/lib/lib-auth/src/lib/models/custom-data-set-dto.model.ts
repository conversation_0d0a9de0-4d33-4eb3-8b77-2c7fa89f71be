/**
 * @fileoverview Model for custom data set
 * <AUTHOR>
 * @version 1.0.0
 */
import { BaseDTO } from 'lib/lib-app-core/src/lib/models/base-dto.model';

export class CustomDataSetDTOModel extends BaseDTO<CustomDataSetDTOModel> {
    CustomDataSetId!: number;
    Dummy!: string;
    CreatedBy!: string | null;
    CreationDate!: string;
    LastUpdatedBy!: string | null;
    LastUpdateDate!: string;
    SiteId!: number;
    MasterEntityId!: number;
    SynchStatus!: boolean;
    Guid!: string | null;
    IsChanged!: boolean;
    IsChangedRecursive!: boolean;
    CustomDataDTOList: any[] = [];

    constructor() {
        super();
    }
}
