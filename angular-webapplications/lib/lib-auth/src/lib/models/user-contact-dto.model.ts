/**
 * @fileoverview Model for user contact
 * <AUTHOR>
 * @version 1.0.0
 */
import { BaseDTO } from 'lib/lib-app-core/src/lib/models/base-dto.model';

export class UserContactDTOModel extends BaseDTO<UserContactDTOModel> {
    Id!: number;
    ProfileId!: number;
    AddressId!: number;
    CountryId!: number;
    ContactSubTypeId!: number;
    ContactTypeId!: number;
    ContactType!: number;
    ContactSubType!: number;
    Attribute1!: string;
    Attribute2!: string;
    IsActive!: boolean;
    IsDefault!: boolean;
    CreatedBy!: string;
    CreationDate!: string;
    LastUpdatedBy!: string;
    LastUpdateDate!: string;
    SiteId!: number;
    MasterEntityId!: number;
    SynchStatus!: boolean;
    Guid!: string;
    UUID!: string;
    WhatsAppEnabled!: boolean;
    IsVerified!: boolean;
    IsChanged!: boolean;

    constructor() {
        super();
    }
}
