/**
 * @fileoverview Model for user profile
 * <AUTHOR>
 * @version 1.0.0
 */
import { BaseDTO } from 'lib/lib-app-core/src/lib/models/base-dto.model';
import { UserContactDTOModel } from './user-contact-dto.model';
import { UserAddressDTOModel } from './user-address-dto.model';

export class UserProfileDTOModel extends BaseDTO<UserProfileDTOModel> {
    Id!: number;
    ProfileTypeId!: number;
    ProfileType!: number;
    Title!: string;
    FirstName!: string;
    MiddleName!: string;
    LastName!: string;
    NickName!: string;
    Notes!: string;
    DateOfBirth!: string;
    Gender!: string;
    Anniversary!: string | null;
    PhotoURL!: string;
    RightHanded!: boolean;
    TeamUser!: boolean;
    UniqueIdentifier!: string;
    IdProofFileURL!: string;
    TaxCode!: string;
    Designation!: string;
    Company!: string;
    UserName!: string;
    Password!: string;
    LastLoginTime!: string | null;

    ContactDTOList: UserContactDTOModel[] = [];
    AddressDTOList: UserAddressDTOModel[] | null = null;

    ProfileContentHistoryDTOList: any = null;

    OptInPromotions!: boolean;
    OptInPromotionsMode!: string;
    OptInLastUpdatedDate!: string | null;
    PolicyTermsAccepted!: boolean;
    IsActive!: boolean;

    CreatedBy!: string;
    CreationDate!: string;
    LastUpdatedBy!: string;
    LastUpdateDate!: string;
    SiteId!: number;
    MasterEntityId!: number;
    SynchStatus!: boolean;
    Guid!: string;

    ExternalSystemReference!: string;
    OptOutWhatsApp!: boolean;
    UserStatus!: string;
    PasswordChangeDate!: string | null;
    InvalidAccessAttempts!: number;
    LockedOutTime!: string | null;
    PasswordChangeOnNextLogin!: boolean;
    IsChanged!: boolean;
    IsChangedRecursive!: boolean;

    constructor() {
        super();
    }
}
