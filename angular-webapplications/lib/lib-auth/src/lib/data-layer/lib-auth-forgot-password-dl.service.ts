/**
 * @fileoverview Data layer service for handling forgot password API calls
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable, tap } from 'rxjs';

interface ForgotPasswordAPIParams {
    Username: string;
}

@Injectable()
export class LibAuthForgotPasswordDL extends ApiServiceBase {
    private _apiParams!: ForgotPasswordAPIParams;

    constructor() {
        super('forgot_password_data', 'FORGOT_PASSWORD');
        this.init();
    }

    buildApiParams(data: ForgotPasswordAPIParams) {
        this._apiParams = data;
    }

    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;
        const payload = this._apiParams;

        return this._http.post<any>(url, payload)
    }
}
