/**
 * @fileoverview Data layer service for handling user details API calls
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import { UserDetailsParams } from '../interfaces/forgot-password.interface';

@Injectable()
export class LibUserDetailDL extends ApiServiceBase {
    private _apiParams!: UserDetailsParams;

    constructor() {
        super('user_details', 'USER_DETAILS');
        this.init();
    }

    buildApiParams(data: UserDetailsParams) {
        this._apiParams = data;
    }

    //Builds the API parameters for the user details request
    //This method is called by the business layer to set the parameters before making the API call
    load(): Observable<any> {
        const { guid } = this._apiParams;
        const url = this.getApiUrl().replace('{CustomerId}', guid);

        return this._http.get<any>(url)
    }
}
