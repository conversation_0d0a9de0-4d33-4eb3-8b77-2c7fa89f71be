/**
 * @fileoverview Concrete implementation of encryption service for login credentials
 * <AUTHOR>
 * @version 2.0.0
 */

import { Injectable } from '@angular/core';
import { Observable, from, switchMap, map, forkJoin } from 'rxjs';
import * as CryptoJS from 'crypto-js';
import { JSEncrypt } from 'jsencrypt';
import { EncryptionServiceBase, BaseEncryptionConfig, BaseEncryptionResult } from './encryption-service-base.service';

// Login-specific interfaces extending base interfaces
export interface LoginEncryptionConfig extends BaseEncryptionConfig {
    loginId: string;
    password: string;
    machineName: string;
}

export interface EncryptedLoginRequest extends BaseEncryptionResult {
    EncryptedUserName: string; // Encrypted login ID
    EncryptedPassword: string; // Encrypted password
    Application: string;
    Version: string;
    Identifier: string;
    SiteId: number;
    EncryptedMachineName: string; // Encrypted machine name
    EncryptedKeyMaterial: string; // Encrypted key material
}

// Server response interfaces
export interface ServerTimeResponse {
    data: number;
}

export interface PublicKeyResponse {
    data: string;
}

export interface PublicKeyRequestParams {
    siteId: string;
    application: string;
    version: string;
    identifier: string;
    format: string;
}

/**
 * Concrete implementation of encryption service specifically for login credentials
 * Implements multi-layer encryption using AES and RSA
 * Extends EncryptionServiceBase to reuse HTTP infrastructure and follow established patterns
 */
@Injectable({
    providedIn: 'root',
})
export class LoginEncryptionService extends EncryptionServiceBase<LoginEncryptionConfig, EncryptedLoginRequest> {
    private aesKey: CryptoJS.lib.WordArray | null = null;
    private jsEncrypt: JSEncrypt | null = null;
    private secondsSinceEpoch: number | null = null;

    constructor() {
        super('login_encryption_config', 'LOGIN_ENCRYPTION', 'LoginEncryption');
    }

    /**
     * Build API-specific parameters for login encryption
     * Implementation of abstract buildAPI method
     */
    buildAPI(config: LoginEncryptionConfig): void {
        // Validate required fields for login encryption
        this.validateConfig(config, [
            'siteId',
            'applicationName',
            'applicationVersion',
            'applicationIdentifier',
            'loginId',
            'password',
            'machineName'
        ]);
        this.logOperation('API parameters built for login encryption');
    }

    /**
     * Initialize encryption by getting public key and timestamp
     * Implementation of abstract load method
     */
    override load(): Observable<void> {
        this.logOperation('Starting encryption initialization');
        return forkJoin({
            time: this.getSecondsSinceEpoch(),
            key: this.loadJSEncrypt().pipe(
                switchMap(() => this.getPublicKey())
            ),
        }).pipe(
            map(() => {
                this.logOperation('Encryption initialization completed');
                return void 0;
            })
        );
    }

    /**
     * Encrypts login credentials using the multi-layer encryption system
     * Implementation of abstract encrypt method
     */
    encrypt(config: LoginEncryptionConfig): Observable<EncryptedLoginRequest> {
        this.buildEncryptionConfig(config);

        return this.load().pipe(
            switchMap(() => this.buildEncryptedLoginRequest(config))
        );
    }

    /**
     * Get timestamp from server
     */
    private getSecondsSinceEpoch(): Observable<void> {
        const url = this.buildEncryptionApiUrl('/Login/SecondsSinceEpoch');

        return this.makeHttpGet<ServerTimeResponse>(url).pipe(
            map((response) => {
                this.secondsSinceEpoch = response.data;
                this.logOperation('Retrieved server timestamp', this.secondsSinceEpoch?.toString());
            })
        );
    }

    /**
     * Load JSEncrypt library dynamically
     */
    private loadJSEncrypt(): Observable<void> {
        return from(
            fetch(
                'https://raw.githubusercontent.com/travist/jsencrypt/master/bin/jsencrypt.js'
            )
                .then((response) => response.text())
                .then((text) => {
                    // Execute the JSEncrypt library
                    new Function(text)();
                    this.jsEncrypt = new (window as any).JSEncrypt();
                    this.logOperation('JSEncrypt loaded successfully');
                })
                .catch((error) => {
                    this.handleEncryptionError(error, 'loading JSEncrypt library');
                })
        );
    }

    /**
     * Get public key from server
     */
    private getPublicKey(): Observable<void> {
        const url = this.buildEncryptionApiUrl('/Login/PublicKey');
        const params = {
            siteId: '1010',
            application: 'WaiverWebsite',
            version: '*********',
            identifier: 'WaiverWebsite',
            format: 'PEM',
        };

        return this.makeHttpGet<PublicKeyResponse>(url, { params }).pipe(
            map((response) => {
                const pemPublicKey = response.data;
                if (this.jsEncrypt) {
                    this.jsEncrypt.setPublicKey(pemPublicKey);
                    this.logOperation('Public key loaded successfully');
                } else {
                    throw new Error('JSEncrypt not initialized');
                }
            })
        );
    }

    /**
     * Build the encrypted login request
     */
    private buildEncryptedLoginRequest(config: LoginEncryptionConfig): Observable<EncryptedLoginRequest> {
        try {
            // Generate AES key if not already generated
            if (!this.aesKey) {
                this.aesKey = CryptoJS.lib.WordArray.random(256/8);
            }

            const base64AesKey = this.aesKey.toString(CryptoJS.enc.Base64);
            const keyMaterial = `${base64AesKey}|${this.secondsSinceEpoch}`;
            this.logOperation('Generated key material');

            const encryptedKeyMaterial = this.rsaEncrypt(keyMaterial);
            const encryptedLoginId = this.aesEncrypt(config.loginId);
            const encryptedPassword = this.aesEncrypt(config.password);
            const encryptedMachineName = this.aesEncrypt(config.machineName);

            const loginRequest: EncryptedLoginRequest = {
                EncryptedUserName: encryptedLoginId,
                EncryptedPassword: encryptedPassword,
                Application: config.applicationName,
                Version: config.applicationVersion,
                Identifier: config.applicationIdentifier,
                SiteId: config.siteId,
                EncryptedMachineName: encryptedMachineName,
                EncryptedKeyMaterial: encryptedKeyMaterial,
            };

            this.logOperation('Built encrypted login request');
            return new Observable((observer) => {
                observer.next(loginRequest);
                observer.complete();
            });
        } catch (error) {
            return new Observable((observer) => {
                observer.error(this.handleEncryptionError(error, 'building encrypted login request'));
            });
        }
    }

    /**
     * AES encryption for sensitive data
     */
    private aesEncrypt(message: string): string {
        if (!this.aesKey) {
            throw new Error('AES key not initialized');
        }

        try {
            const iv = CryptoJS.enc.Hex.parse('00000000000000000000000000000000');
            const encrypted = CryptoJS.AES.encrypt(message, this.aesKey, {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7,
            });

            const result = encrypted.ciphertext.toString(CryptoJS.enc.Base64);
            this.logOperation('AES encrypted data', `${message.substring(0, 3)}...`);
            return result;
        } catch (error) {
            throw this.handleEncryptionError(error, 'AES encryption');
        }
    }

    /**
     * RSA encryption for key material
     */
    private rsaEncrypt(message: string): string {
        if (!this.jsEncrypt) {
            throw new Error('JSEncrypt not initialized');
        }

        try {
            const result = this.jsEncrypt.encrypt(message);
            if (!result) {
                throw new Error('RSA encryption failed - no result returned');
            }
            this.logOperation('RSA encrypted key material');
            return result;
        } catch (error) {
            throw this.handleEncryptionError(error, 'RSA encryption');
        }
    }

    /**
     * Reset encryption state (useful for testing or re-initialization)
     */
    resetEncryptionState(): void {
        this.aesKey = null;
        this.jsEncrypt = null;
        this.secondsSinceEpoch = null;
        this.logOperation('Encryption state reset');
    }
}