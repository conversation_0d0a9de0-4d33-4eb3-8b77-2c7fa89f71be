/**
 * @fileoverview Abstract base class for encryption services extending ApiServiceBase pattern
 * <AUTHOR>
 * @version 2.0.0
 */

import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiServiceBase } from 'lib-app-core';

// Base interfaces for encryption services
export interface BaseEncryptionConfig {
    siteId: number;
    applicationName: string;
    applicationVersion: string;
    applicationIdentifier: string;
}

export interface BaseEncryptionResult {
    [key: string]: string | number;
}

/**
 * Abstract base class for encryption services
 * Extends ApiServiceBase to reuse HTTP infrastructure and follow established patterns
 * Uses generics to ensure type safety across different encryption implementations
 */
@Injectable()
export abstract class EncryptionServiceBase<
    TConfig extends BaseEncryptionConfig,
    TResult extends BaseEncryptionResult
> extends ApiServiceBase {
    private _encryptionConfig: TConfig | null = null;

    constructor(
        dataTransferKey: string,
        apiEndpoint: string,
        private _encryptionType: string
    ) {
        super(dataTransferKey, apiEndpoint);
        // Don't call init() as encryption services don't need data caching by default
    }

    /**
     * Abstract method to initialize encryption (load keys, timestamps, etc.)
     * Must be implemented by concrete encryption services
     * This replaces the load() method from ApiServiceBase for encryption-specific initialization
     */
    abstract override load(): Observable<void>;

    /**
     * Abstract method to perform the main encryption operation
     * Must be implemented by concrete encryption services
     */
    abstract encrypt(config: TConfig): Observable<TResult>;

    /**
     * Abstract method to build API-specific parameters
     * Similar to buildApiParams in data layer services
     * This allows each encryption service to define its own parameter structure
     */
    abstract buildAPI(config: TConfig): void;

    /**
     * Build encryption configuration parameters
     * Similar to buildApiParams in ApiServiceBase
     */
    buildEncryptionConfig(config: TConfig): void {
        this._encryptionConfig = config;
        this.buildAPI(config); // Call the abstract buildAPI method
    }

    /**
     * Get the current encryption configuration
     */
    protected get encryptionConfig(): TConfig {
        if (!this._encryptionConfig) {
            throw new Error('Encryption configuration not set. Call buildEncryptionConfig() first.');
        }
        return this._encryptionConfig;
    }

    /**
     * Check if encryption configuration is set
     */
    protected get hasEncryptionConfig(): boolean {
        return this._encryptionConfig !== null;
    }

    /**
     * Get the encryption type identifier
     */
    protected get encryptionType(): string {
        return this._encryptionType;
    }

    /**
     * Helper method to build API URL for encryption endpoints
     * Uses direct URL construction for encryption-specific endpoints
     */
    protected buildEncryptionApiUrl(endpoint: string, params?: Record<string, string | number>): string {
        let url = `${this._envService.parafaitApiBaseUrl}${endpoint}`;

        if (params) {
            const queryParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                queryParams.append(key, String(value));
            });
            url += `?${queryParams.toString()}`;
        }

        return url;
    }

    /**
     * Helper method to make HTTP GET requests with proper typing
     * Reuses the existing HTTP client from ApiServiceBase
     */
    protected makeHttpGet<T>(url: string, options?: { params?: Record<string, string | number> }): Observable<T> {
        if (options?.params) {
            const queryParams = new URLSearchParams();
            Object.entries(options.params).forEach(([key, value]) => {
                queryParams.append(key, String(value));
            });
            url += `?${queryParams.toString()}`;
        }

        return this._http.get<T>(url);
    }

    /**
     * Helper method to make HTTP POST requests with proper typing
     * Reuses the existing HTTP client from ApiServiceBase
     */
    protected makeHttpPost<T>(url: string, body: unknown): Observable<T> {
        return this._http.post<T>(url, body);
    }

    /**
     * Validate that required configuration fields are present
     */
    protected validateConfig(config: TConfig, requiredFields: (keyof TConfig)[]): void {
        const missingFields = requiredFields.filter(field => 
            config[field] === undefined || config[field] === null || config[field] === ''
        );

        if (missingFields.length > 0) {
            throw new Error(`Missing required configuration fields: ${missingFields.join(', ')}`);
        }
    }

    /**
     * Log encryption operations (can be overridden by concrete implementations)
     */
    protected logOperation(operation: string, details?: string): void {
        console.log(`[${this.encryptionType}] ${operation}${details ? ': ' + details : ''}`);
    }

    /**
     * Handle encryption errors consistently
     */
    protected handleEncryptionError(error: unknown, operation: string): never {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const fullMessage = `${this.encryptionType} ${operation} failed: ${errorMessage}`;
        console.error(fullMessage, error);
        throw new Error(fullMessage);
    }
}
