/**
 * @fileoverview DateDropdownPickerComponent interface
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
/**
 * Available date formats for the dropdown picker
 */
export type DateFormat =
    | 'dd-mm-yyyy'
    | 'mm-dd-yyyy'
    | 'yyyy-mm-dd'
    | 'dd-yyyy-mm'
    | 'mm-yyyy-dd'
    | 'yyyy-dd-mm';

/**
 * Interface representing a date value with day, month, and year components
 */
export interface DateDropdownValue {
    day: number | null;
    month: number | null;
    year: number | null;
}

/**
 * Interface for dropdown field configuration
 */
export interface DateFieldConfig {
    type: 'day' | 'month' | 'year';
    width: string;
    placeholder: string;
    options: Array<{ value: number | null; label: string }>;
}

/**
 * Interface for dropdown option
 */
export interface DateOption {
    value: number | null;
    label: string;
}
