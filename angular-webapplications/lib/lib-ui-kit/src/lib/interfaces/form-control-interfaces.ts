import { Component, TemplateRef } from "@angular/core";

export type ErrorMessage<T> = {
    [K in keyof T]?: Record<
        string,
        string | TemplateRef<Component> | undefined
    >;
};


export type FieldDescription<T> = {
    [K in keyof T]?: string;
};

export interface DropdownOption {
    label: string;
    value: string;
}

export interface CalendarDay {
    date: Date;
    isCurrentMonth: boolean;
    isSelected: boolean;
    isToday: boolean;
}