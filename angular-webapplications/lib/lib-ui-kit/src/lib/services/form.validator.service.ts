/**
 * @fileoverview Service for validating form fields
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

@Injectable()
export class FormValidatorService {
    /**
     * Service for validating form fields.
     *
     * This service provides methods for creating validator functions for various form fields,
     * Validators functions:
     *  - passwordMatchValidator : Returns a validator function that checks if password and confirm password match
     */
    /**
     * Factory function to create a password match validator.
     *
     * @param {string} passwordField - The name of the password field
     * @param {string} confirmPasswordField - The name of the confirm password field
     * @returns {ValidatorFn} A validator function that checks if password and confirm password match
     */
    passwordMatchValidator(
        passwordField: string,
        confirmPasswordField: string
    ): ValidatorFn {
        return (form: AbstractControl): ValidationErrors | null => {
            const password = form.get(passwordField)?.value;
            const confirmPassword = form.get(confirmPasswordField)?.value;

            if (password !== confirmPassword) {
                form.get(confirmPasswordField)?.setErrors({
                    passwordMismatch: true,
                });
                return { passwordMismatch: true };
            }

            return null;
        };
    }
}
