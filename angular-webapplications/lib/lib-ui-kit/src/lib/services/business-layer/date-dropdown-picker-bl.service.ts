/**
 * @fileoverview DateDropdownPickerBLService is a service that provides business logic for the DateDropdownPickerComponent
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { Injectable, computed, signal } from '@angular/core';
import {
    DateDropdownValue,
    DateFormat,
    DateFieldConfig,
} from '../../interface/date-dropdown-picker.interface';

@Injectable()
export class DateDropdownPickerBLService {
    /**
     * Array of months with display names and values
     * Used for populating the month dropdown
     */
    readonly months = [
        { name: 'Jan', value: 1 },
        { name: 'Feb', value: 2 },
        { name: 'Mar', value: 3 },
        { name: 'Apr', value: 4 },
        { name: 'May', value: 5 },
        { name: 'Jun', value: 6 },
        { name: 'Jul', value: 7 },
        { name: 'Aug', value: 8 },
        { name: 'Sep', value: 9 },
        { name: 'Oct', value: 10 },
        { name: 'Nov', value: 11 },
        { name: 'Dec', value: 12 },
    ] as const;

    /**
     * Array of years from 1930 to 2050
     * Used for populating the year dropdown
     */
    readonly years = Array.from(
        { length: 2050 - 1930 + 1 },
        (_, i) => 1930 + i
    );

    // State signals
    readonly selectedDay = signal<number | null>(null);
    readonly selectedMonth = signal<number | null>(null);
    readonly selectedYear = signal<number | null>(null);
    private readonly touched = signal<boolean>(false);
    private readonly format = signal<DateFormat>('dd-mm-yyyy');

    /**
     * Sets the date format for the dropdown
     * @param format - The date format to use
     */
    setFormat(format: DateFormat): void {
        this.format.set(format);
    }

    /**
     * Gets the current date format
     * @returns The current date format
     */
    getFormat(): DateFormat {
        return this.format();
    }

    /**
     * Computed field configuration based on the current format
     * Determines the order and configuration of day, month, and year fields
     *
     * @returns Array of field configurations in the correct order
     */
    readonly fieldConfig = computed(() => {
        const currentFormat = this.format();
        const configs: DateFieldConfig[] = [];

        // Parse format and create field configurations
        const parts = currentFormat.split('-');

        parts.forEach((part) => {
            switch (part) {
                case 'dd':
                    configs.push({
                        type: 'day',
                        width: 'w-20',
                        placeholder: 'Day',
                        options: this.getDayOptions(),
                    });
                    break;
                case 'mm':
                    configs.push({
                        type: 'month',
                        width: 'w-28',
                        placeholder: 'Month',
                        options: this.getMonthOptions(),
                    });
                    break;
                case 'yyyy':
                    configs.push({
                        type: 'year',
                        width: 'w-24',
                        placeholder: 'Year',
                        options: this.getYearOptions(),
                    });
                    break;
            }
        });

        return configs;
    });

    /**
     * Gets day options for dropdown
     * @returns Array of day options
     */
    private getDayOptions(): Array<{ value: number | null; label: string }> {
        const options: Array<{ value: number | null; label: string }> = [
            { value: null, label: 'Day' },
        ];
        for (let i = 1; i <= this.daysInMonth(); i++) {
            options.push({ value: i, label: i.toString() });
        }
        return options;
    }

    /**
     * Gets month options for dropdown
     * @returns Array of month options
     */
    private getMonthOptions(): Array<{ value: number | null; label: string }> {
        const options: Array<{ value: number | null; label: string }> = [
            { value: null, label: 'Month' },
        ];
        this.months.forEach((month) => {
            options.push({ value: month.value as number, label: month.name });
        });
        return options;
    }

    /**
     * Gets year options for dropdown (4-digit)
     * @returns Array of year options
     */
    private getYearOptions(): Array<{ value: number | null; label: string }> {
        const options: Array<{ value: number | null; label: string }> = [
            { value: null, label: 'Year' },
        ];
        this.years.forEach((year) => {
            options.push({ value: year as number, label: year.toString() });
        });
        return options;
    }

    /**
     * Computed value for the number of days in the selected month/year
     * Handles leap years and different month lengths
     *
     * @returns Number of days in the current month (28, 29, 30, or 31)
     */
    readonly daysInMonth = computed(() => {
        const month = this.selectedMonth();
        const year = this.selectedYear();

        if (!month || !year) return 31;

        // February
        if (month === 2) {
            return this.isLeapYear(year) ? 29 : 28;
        }

        // Months with 30 days
        if ([4, 6, 9, 11].includes(month)) return 30;

        return 31;
    });

    /**
     * Computed array of day options based on the selected month/year
     * Dynamically updates when month or year changes
     *
     * @returns Array of day numbers (1 to daysInMonth)
     */
    readonly dayOptions = computed(() =>
        Array.from({ length: this.daysInMonth() }, (_, i) => i + 1)
    );

    /**
     * Computed current date value object
     * Combines all selected values into a single object
     *
     * @returns DateDropdownValue object with current selections
     */
    readonly currentValue = computed(() => ({
        day: this.selectedDay(),
        month: this.selectedMonth(),
        year: this.selectedYear(),
    }));

    /**
     * Computed validation state for the current date
     * Checks if all fields are selected and the date is valid
     *
     * @returns true if the date is complete and valid, false otherwise
     */
    readonly isValidDate = computed(() => {
        const { day, month, year } = this.currentValue();

        // All three fields must be selected
        if (!day || !month || !year) return false;

        // Check if day is valid for month/year
        if (day > this.daysInMonth()) return false;

        // Additional validation for February 29th
        if (month === 2 && day === 29 && !this.isLeapYear(year)) {
            return false;
        }

        return true;
    });

    /**
     * Computed error display logic
     * Determines when to show validation errors based on user interaction and date validity
     *
     * Error display rules:
     * - No error if component hasn't been touched
     * - No error if no fields are selected
     * - Show error if some fields are selected but not all
     * - Show error if all fields are selected but date is invalid
     *
     * @returns true if error should be displayed, false otherwise
     */
    readonly shouldShowError = computed(() => {
        if (!this.touched()) return false;

        const { day, month, year } = this.currentValue();
        const selectedCount = [day, month, year].filter(
            (val) => val !== null
        ).length;

        // No selection - don't show error
        if (selectedCount === 0) return false;

        // Partial selection - show error
        if (selectedCount < 3) return true;

        // All selected but invalid date - show error
        return !this.isValidDate();
    });

    /**
     * Sets the initial value for the date picker
     * Used when the component is initialized with a pre-existing value
     *
     * @param value - The initial date value to set
     */
    setInitialValue(value: DateDropdownValue | null): void {
        if (value) {
            this.selectedDay.set(value.day);
            this.selectedMonth.set(value.month);
            this.selectedYear.set(value.year);
        }
    }

    /**
     * Sets the touched state of the component
     * Used to track user interaction for validation display
     *
     * @param value - Whether the component has been touched
     */
    setTouched(value: boolean): void {
        this.touched.set(value);
    }

    /**
     * Handles day selection changes
     * Updates the selected day and marks component as touched
     *
     * @param value - The selected day value (1-31) or null
     */
    onDayChange(value: number | null): void {
        this.selectedDay.set(value);
        this.touched.set(true);
    }

    /**
     * Handles month selection changes
     * Updates the selected month, adjusts day if needed, and marks component as touched
     *
     * @param value - The selected month value (1-12) or null
     */
    onMonthChange(value: number | null): void {
        this.selectedMonth.set(value);
        this.adjustDayIfNeeded();
        this.touched.set(true);
    }

    /**
     * Handles year selection changes
     * Updates the selected year, adjusts day if needed, and marks component as touched
     *
     * @param value - The selected year value (1930-2050) or null
     */
    onYearChange(value: number | null): void {
        this.selectedYear.set(value);
        this.adjustDayIfNeeded();
        this.touched.set(true);
    }

    /**
     * Writes a new value to the date picker
     * Used by ControlValueAccessor to set external values
     *
     * @param value - The date value to set
     */
    writeValue(value: DateDropdownValue | null): void {
        this.selectedDay.set(value?.day ?? null);
        this.selectedMonth.set(value?.month ?? null);
        this.selectedYear.set(value?.year ?? null);
    }

    /**
     * Determines if a given year is a leap year
     * Uses the standard leap year rules: divisible by 4, but not by 100 unless also by 400
     *
     * @param year - The year to check
     * @returns true if the year is a leap year, false otherwise
     */
    private isLeapYear(year: number): boolean {
        return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
    }

    /**
     * Adjusts the selected day if it's invalid for the current month/year
     * Called when month or year changes to ensure day selection remains valid
     * Resets day to null if current selection exceeds days in month
     */
    private adjustDayIfNeeded(): void {
        const currentDay = this.selectedDay();
        if (currentDay && currentDay > this.daysInMonth()) {
            this.selectedDay.set(null);
        }
    }
}
