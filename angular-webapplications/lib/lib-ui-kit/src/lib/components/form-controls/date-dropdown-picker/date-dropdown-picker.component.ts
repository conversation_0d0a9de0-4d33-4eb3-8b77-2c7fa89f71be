/**
 * @fileoverview DateDropdownPickerComponent is a component that allows the user to select a date from a dropdown menu
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { Component, effect, input, output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
    FormsModule,
    ControlValueAccessor,
    NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { DateDropdownPickerBLService } from '../../../services/business-layer/date-dropdown-picker-bl.service';
import {
    DateDropdownValue,
    DateFormat,
} from '../../../interface/date-dropdown-picker.interface';

@Component({
    selector: 'lib-date-dropdown-picker',
    standalone: true,
    imports: [CommonModule, FormsModule],
    templateUrl: './date-dropdown-picker.component.html',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: DateDropdownPickerComponent,
            multi: true,
        },
        DateDropdownPickerBLService,
    ],
})
export class DateDropdownPickerComponent implements ControlValueAccessor {
    private readonly _dateDropDownBlService = inject(DateDropdownPickerBLService);

    label = input<string>('Date of birth');
    required = input<boolean>(false);
    errorMessage = input<string | null>('Enter valid date');
    name = input<string>('');
    initialValue = input<DateDropdownValue | null>(null);
    format = input<DateFormat>('dd-mm-yyyy');
    dateChange = output<DateDropdownValue | null>();

    // Expose business layer properties
    readonly months = this._dateDropDownBlService.months;
    readonly years = this._dateDropDownBlService.years;
    readonly daysInMonth = this._dateDropDownBlService.daysInMonth;
    readonly dayOptions = this._dateDropDownBlService.dayOptions;
    readonly currentValue = this._dateDropDownBlService.currentValue;
    readonly isValidDate = this._dateDropDownBlService.isValidDate;
    readonly shouldShowError = this._dateDropDownBlService.shouldShowError;
    readonly fieldConfig = this._dateDropDownBlService.fieldConfig;

    // Expose selected values for template binding
    readonly selectedDay = this._dateDropDownBlService.selectedDay;
    readonly selectedMonth = this._dateDropDownBlService.selectedMonth;
    readonly selectedYear = this._dateDropDownBlService.selectedYear;

    // ControlValueAccessor properties
    private onChange: (value: DateDropdownValue | null) => void = () => { };
    private onTouched: () => void = () => { };

    constructor() {
        // Single effect to handle all reactive updates
        effect(() => {
            // Set format
            const currentFormat = this.format();
            this._dateDropDownBlService.setFormat(currentFormat);

            // Handle initial value
            const initialVal = this.initialValue();
            if (initialVal) {
                this._dateDropDownBlService.setInitialValue(initialVal);
            }

            // Emit current value
            this.emitDate();
        });
    }

    // Public methods
    onBlur(): void {
        this._dateDropDownBlService.setTouched(true);
        this.onTouched();
    }

    onDayChange(value: number | null): void {
        this._dateDropDownBlService.onDayChange(value);
    }

    onMonthChange(value: number | null): void {
        this._dateDropDownBlService.onMonthChange(value);
    }

    onYearChange(value: number | null): void {
        this._dateDropDownBlService.onYearChange(value);
    }

    /**
     * Get the selected value for a specific field type
     * @param fieldType - The type of field (day, month, year)
     * @returns The selected value for the field
     */
    getSelectedValue(fieldType: 'day' | 'month' | 'year'): number | null {
        switch (fieldType) {
            case 'day':
                return this.selectedDay();
            case 'month':
                return this.selectedMonth();
            case 'year':
                return this.selectedYear();
            default:
                return null;
        }
    }

    /**
     * Handle field value changes dynamically
     * @param fieldType - The type of field that changed
     * @param value - The new value
     */
    onFieldChange(
        fieldType: 'day' | 'month' | 'year',
        value: number | null
    ): void {
        switch (fieldType) {
            case 'day':
                this.onDayChange(value);
                break;
            case 'month':
                this.onMonthChange(value);
                break;
            case 'year':
                this.onYearChange(value);
                break;
        }
    }

    /**
     * emit date
     */
    private emitDate(): void {
        const value = this.isValidDate() ? this.currentValue() : null;
        this.dateChange.emit(value);
        this.onChange(value);
    }

    // ControlValueAccessor implementation
    writeValue(value: DateDropdownValue | null): void {
        this._dateDropDownBlService.writeValue(value);
    }

    /**
     * register on change
     * @param fn
     */
    registerOnChange(fn: (value: DateDropdownValue | null) => void): void {
        this.onChange = fn;
    }

    /**
     * register on touched
     * @param fn
     */
    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }
}
