<div class="flex flex-col w-full">
    <label class="block text-sm font-medium text-gray-700 mb-1">
        {{ label() }}@if (required()) {<span class="text-red-500">*</span>}
    </label>
    <div class="flex gap-2 w-full">
        @for (field of fieldConfig(); track field.type) {
        <select class="border rounded-xl px-2 py-3 focus:outline-none focus:ring-2 focus:ring-primary"
            [class]="field.width" [ngModel]="getSelectedValue(field.type)"
            (ngModelChange)="onFieldChange(field.type, $event)" (blur)="onBlur()">
            @for (option of field.options; track option.value) {
            <option [ngValue]="option.value">{{ option.label }}</option>
            }
        </select>
        }
    </div>
    @if (shouldShowError() && errorMessage()) {
    <span class="text-xs text-red-500 mt-1">{{ errorMessage() }}</span>
    }
</div>