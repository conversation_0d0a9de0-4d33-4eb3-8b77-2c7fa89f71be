import { CommonModule } from '@angular/common';
import {
    Component,
    computed,
    forwardRef,
    inject,
    Injector,
    input,
    OnInit,
    signal,
    TemplateRef,
    ChangeDetectionStrategy
} from '@angular/core';
import {
    ControlValueAccessor,
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { isTemplateRef } from '../../../utils';

@Component({
    selector: 'lib-text-input',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './text-input.component.html',
    styleUrl: './text-input.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => TextInputComponent),
            multi: true,
        },
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TextInputComponent implements ControlValueAccessor, OnInit {
    id = input<string>('');
    type = input<'text' | 'email' | 'password'>('text');
    name = input<string>('');
    label = input<string>('');
    placeholder = input<string>('');
    required = input<boolean>(false);
    showPasswordToggle = input<boolean>(false);
    errorMessages = input<Record<string, string | TemplateRef<Component> | undefined>>();
    icon = input<string>('');
    iconAlt = input<string>('');
    customClass = input<string>('');
    description = input<string | undefined>();
    onIconClick = input<(() => void) | null>(null);
    showPassword = false;
    value = signal<string | null>('');
    isDisabled = false;
    isTemplateRef = isTemplateRef;
    private ngControl: NgControl | null = null;
    private injector = inject(Injector);

    private onChange: (value: string | null) => void = () => { };
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        const ngControl = this.injector.get(NgControl, null);
        if (ngControl) {
            this.ngControl = ngControl;
        }
    }

    get isRequired(): boolean {
        return (
            this.required() ||
            !!this.ngControl?.control?.hasValidator?.(Validators.required)
        );
    }

    writeValue(value: string | null): void {
        this.value.set(value);
    }

    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }

    onInputChange(event: Event): void {
        const input = event.target as HTMLInputElement;
        const value = input.value;
        this.value.set(value);
        this.onChange(value);
    }

    onBlur(): void {
        this.onTouched();
    }

    togglePasswordVisibility(): void {
        this.showPassword = !this.showPassword;
    }

    getInputType(): string {
        if (this.type() === 'password') {
            return this.showPassword ? 'text' : 'password';
        }
        return this.type();
    }

    hasError(): boolean {
        return (
            !!this.ngControl?.control?.invalid &&
            !!this.ngControl?.control?.touched
        );
    }

    get errorMessage() {
        const errors = this.ngControl?.control?.errors;
        if (!errors) return null;

        const errorKeys = Object.keys(errors);
        if (errorKeys.length > 0) {
            const firstErrorKey = errorKeys[0];
            return this.errorMessages()?.[firstErrorKey] ?? null;
        }
        return null;
    }

    get customClasses() {
        let className = '';
        if (this.hasError()) {
            className = 'border-none ring-1 ring-feedback-error';
        } else if (this.description()) {
            className = 'border-none ring-1 ring-surface';
        }
        if (this.customClass()) {
            className += ` ${this.customClass()}`;
        }
        return className;
    }
}
