<div>
    @if (label()) {
    <label class="block text-xs font-medium text-primary mb-2" [for]="id()">
        {{ label() }}
        @if (isRequired) {
        <span class="text-red-500">*</span>
        }
    </label>
    }

    <div class="relative flex items-center">
        <input [id]="id()" [type]="getInputType()" [name]="name()" [placeholder]="placeholder()"
            [ngClass]="customClasses" [value]="value()" [disabled]="isDisabled" (input)="onInputChange($event)"
            (blur)="onBlur()"
            class="w-full text-sm py-3.5 px-4 border border-surface rounded-xl focus:outline-none focus:ring-1 focus:ring-secondary-blue placeholder:text-neutral-dark" />
        @if (type() === 'password' && showPasswordToggle()) {
        <button type="button" (click)="togglePasswordVisibility()"
            class="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-500">
            @if (!showPassword) {
            <img src="assets/icons/eye.svg" alt="open-eye" />
            } @else {
            <img src="assets/icons/eye-close.svg" alt="close-eye" />
            }
        </button>
        } @if (icon() && onIconClick()) {
        <button type="button" (click)="onIconClick()?.()"
            class="absolute inset-y-0 right-0 flex items-center py-3.5 px-4 cursor-pointer">
            <img [src]="icon()" [alt]="iconAlt()" />
        </button>
        } @else if (icon()) {
        <img class="absolute inset-y-0 right-0 flex items-center py-3.5 px-4" [src]="icon()" [alt]="iconAlt()" />
        }
    </div>

    @if (hasError()) {
    <div class="text-feedback-error text-xs mt-2">
        @if (isTemplateRef(errorMessage)) {
        <ng-container *ngTemplateOutlet="$any(errorMessage)"></ng-container>
        } @else {
        {{ errorMessage }}
        }
    </div>
    } @else if (description()) {
    <div class="text-neutral-dark text-xs mt-2">{{ description() }}</div>
    }
</div>