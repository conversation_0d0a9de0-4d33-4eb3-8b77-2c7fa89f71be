/**
 * @fileoverview
 * A date picker component that allows the user to select a date.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-22
 */
import { CommonModule } from '@angular/common';
import {
    Component,
    ElementRef,
    forwardRef,
    inject,
    Inject,
    Injector,
    input,
    OnInit,
    output,
    signal,
    TemplateRef,
    viewChild,
    ChangeDetectionStrategy
} from '@angular/core';
import {
    ControlValueAccessor,
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import {
    eachDayOfInterval,
    endOfMonth,
    format,
    getMonth,
    getYear,
    isToday,
    isValid,
    parse,
    startOfMonth,
} from 'date-fns';
import { ClickOutsideDirective } from '../../../directives/click-outside.directive';
import { CalendarDay } from '../../../interfaces/form-control-interfaces';
import { isTemplateRef } from '../../../utils';



export const DATE_FORMAT = 'dd-MM-yyyy';
export const CALENDAR_GRID_COLUMNS = 7;
export const CALENDAR_GRID_ROWS = 6;
export const YEARS_OFFSET = 100;

@Component({
    selector: 'lib-date-picker',
    imports: [CommonModule, ReactiveFormsModule, ClickOutsideDirective],
    templateUrl: './date-picker.component.html',
    styleUrl: './date-picker.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => DatePickerComponent),
            multi: true,
        },
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DatePickerComponent implements ControlValueAccessor, OnInit {
    id = input<string>('');
    label = input<string>('');
    name = input<string>('');
    placeholder = input<string>('');
    required = input<boolean>(false);
    disabled = input<boolean>(false);
    format = input<string>(DATE_FORMAT);
    errorMessages = input<Record<string, string | TemplateRef<Component> | undefined>>();
    minDate = input<Date | null>(null);
    maxDate = input<Date | null>(null);
    minYear = input<number>(new Date().getFullYear() - YEARS_OFFSET);
    maxYear = input<number>(new Date().getFullYear());
    position = input<'top' | 'bottom'>('bottom');
    rightOffset = input<number | null>(null);
    customClasses = input<string>('');
    // When this enabled, the datepicker font size is reduced to 10px when width is less than 390px
    useResponsiveFontSize = input<boolean>(false);

    // When this enabled, the datepicker cell dimensions are reduced to 1.75rem when height is less than 720px
    useResponsiveHeight = input<boolean>(false);
    dateSelected = output<Date | null>();

    calendarContainer = viewChild('calendarContainer');

    showCalendar = false;
    showMonthDropdown = false;
    showYearDropdown = false;

    currentDate = new Date();
    selectedDate: Date | null = null;
    displayMonth: number;
    displayYear: number;

    weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];

    years: number[] = [];
    calendarDays: CalendarDay[] = [];

    value = signal<string | null>(null);
    isControlDisabled = false;
    isTemplateRef = isTemplateRef;

    private ngControl: NgControl | null = null;
    private injector = inject(Injector);

    constructor(@Inject(ElementRef) private elementRef: ElementRef) {
        this.displayMonth = this.currentDate.getMonth();
        this.displayYear = this.currentDate.getFullYear();
    }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onChange: (value: string | null) => void = () => { };
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        const ngControl = this.injector.get(NgControl, null);
        if (ngControl) {
            this.ngControl = ngControl;
        }
        this.generateYears();
        this.generateCalendarDays();
    }

    /**
     * Checks if the date picker is required.
     */
    get isRequired(): boolean {
        return (
            this.required() ||
            !!this.ngControl?.control?.hasValidator?.(Validators.required)
        );
    }

    /**
     * Writes the value to the date picker.
     */
    writeValue(value: string | null): void {
        this.value.set(value);
        if (value && typeof value === 'string') {
            try {
                const parsedDate = parse(value, this.format(), new Date());
                if (isValid(parsedDate)) {
                    this.selectedDate = parsedDate;
                    this.displayMonth = getMonth(parsedDate);
                    this.displayYear = getYear(parsedDate);
                    const formattedDate = this.formatDate(parsedDate); // Format the date before updating the control
                    this.ngControl?.control?.setValue(formattedDate, { emitEvent: false });
                }
            } catch (error) {
                console.error('Error parsing date:', error);
                this.selectedDate = null;
            }
        } else if (value === null) {
            this.selectedDate = null;
            this.ngControl?.control?.setValue('', { emitEvent: false });
        }
        this.generateCalendarDays();
    }

    /**
     * Registers a change handler for the date picker.
     */
    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn;
    }

    /**
     * Registers a touched handler for the date picker.
     */
    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    /**
     * Sets the disabled state of the date picker.
     */
    setDisabledState(isDisabled: boolean): void {
        this.isControlDisabled = isDisabled;
    }

    /**
     * Handles the blur event for the date picker.
     */
    onBlur(): void {
        if (!this.showCalendar) {
            this.onTouched();
        }
    }

    /**
     * Checks if the date picker has an error.
     */
    hasError(): boolean {
        return (
            !!this.ngControl?.control?.invalid &&
            !!this.ngControl.control.touched
        );
    }

    /**
     * Opens the calendar.
     */
    openCalendar(): void {
        this.showCalendar = true;
    }

    /**
     * Closes the calendar.
     */
    closeCalendar(): void {
        this.showCalendar = false;
        this.onBlur();
    }

    /**
     * Toggles the month dropdown.
     */
    toggleMonthDropdown(event: Event): void {
        event.stopPropagation();
        this.showMonthDropdown = !this.showMonthDropdown;
        this.showYearDropdown = false;
    }

    /**
     * Toggles the year dropdown.
     */
    toggleYearDropdown(event: Event): void {
        event.stopPropagation();
        this.showYearDropdown = !this.showYearDropdown;
        this.showMonthDropdown = false;
    }

    /**
     * Selects a month.
     * @param {number} month - The month to select.
     * @param {Event} event - The event to stop propagation.
     */
    selectMonth(month: number, event: Event): void {
        event.stopPropagation();
        this.displayMonth = month;

        if (this.selectedDate) {
            // Preserve the day of the month
            const day = this.selectedDate.getDate();
            const daysInMonth = new Date(
                this.displayYear,
                this.displayMonth + 1,
                0
            ).getDate();
            // Ensure the day is within the valid range of the new month
            const newDay = Math.min(day, daysInMonth);
            this.selectedDate = new Date(
                this.displayYear,
                this.displayMonth,
                newDay
            );
            this.ngControl?.control?.setValue(this.formatDate(this.selectedDate));
            this.onChange(this.formatDate(this.selectedDate)); // Notify Angular Forms
        }

        this.showMonthDropdown = false;
        this.generateCalendarDays();
    }

    /**
     * Selects a year.
     * @param {number} year - The year to select.
     * @param {Event} event - The event to stop propagation.
     */
    selectYear(year: number, event: Event): void {
        event.stopPropagation();
        this.displayYear = year;

        if (this.selectedDate) {
            // Preserve the day of the month
            const day = this.selectedDate.getDate();
            const month = this.selectedDate.getMonth();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            // Ensure the day is within the valid range
            const newDay = Math.min(day, daysInMonth);
            this.selectedDate = new Date(year, month, newDay);
            this.ngControl?.control?.setValue(this.formatDate(this.selectedDate));
        }

        this.showYearDropdown = false;
        this.generateCalendarDays();
    }

    /**
     * Selects a date.
     * @param {CalendarDay} day - The date to select.
     */
    selectDate(day: CalendarDay): void {
        if (!this.isDisabled(day)) {
            this.selectedDate = day.date;
            const formattedDate = this.formatDate(day.date);
            this.ngControl?.control?.setValue(formattedDate);
            this.onChange(formattedDate);
            this.dateSelected.emit(day.date);
            this.showCalendar = false;
            this.onBlur();
            this.generateCalendarDays();
        }
    }

    /**
     * Generates the years.
     */
    generateYears(): void {
        for (let year = this.minYear(); year <= this.maxYear(); year++) {
            this.years.push(year);
        }
        this.years.reverse(); // Display most recent years first
    }

    /**
     * Generates the calendar days.
     */
    generateCalendarDays(): void {
        this.calendarDays = [];

        // Get first day of the month
        const firstDayOfMonth = startOfMonth(
            new Date(this.displayYear, this.displayMonth)
        );
        const lastDayOfMonth = endOfMonth(
            new Date(this.displayYear, this.displayMonth)
        );

        const firstDayWeekday = firstDayOfMonth.getDay();

        // Add days from previous month to fill the first row
        for (let i = 0; i < firstDayWeekday; i++) {
            const date = new Date(
                this.displayYear,
                this.displayMonth - 1,
                new Date(this.displayYear, this.displayMonth, 0).getDate() -
                (firstDayWeekday - i - 1)
            );

            this.calendarDays.push({
                date,
                isCurrentMonth: false,
                isSelected: this.isSelectedDate(date),
                isToday: isToday(date),
            });
        }

        // Add days of current month
        const daysOfMonth = eachDayOfInterval({
            start: firstDayOfMonth,
            end: lastDayOfMonth,
        });

        for (const date of daysOfMonth) {
            this.calendarDays.push({
                date,
                isCurrentMonth: true,
                isSelected: this.isSelectedDate(date),
                isToday: isToday(date),
            });
        }

        const remainingCells =
            CALENDAR_GRID_COLUMNS * CALENDAR_GRID_ROWS -
            this.calendarDays.length;
        for (let i = 1; i <= remainingCells; i++) {
            const date = new Date(this.displayYear, this.displayMonth + 1, i);
            this.calendarDays.push({
                date,
                isCurrentMonth: false,
                isSelected: this.isSelectedDate(date),
                isToday: isToday(date),
            });
        }
    }

    /**
     * Checks if a date is selected.
     * @param {Date} date - The date to check.
     */
    isSelectedDate(date: Date): boolean {
        if (!this.selectedDate) return false;
        return (
            date.getDate() === this.selectedDate.getDate() &&
            date.getMonth() === this.selectedDate.getMonth() &&
            date.getFullYear() === this.selectedDate.getFullYear()
        );
    }

    /**
     * Formats a date.
     * @param {Date} date - The date to format.
     */
    formatDate(date: Date): string {
        return format(date, DATE_FORMAT);
    }

    /**
     * Parses the input date.
     */
    parseInputDate(event: Event): void {
        const input = (event.target as HTMLInputElement).value;
        const parsed = parse(input, DATE_FORMAT, new Date());
        if (isValid(parsed)) {
            this.selectedDate = new Date(parsed);
            this.displayMonth = getMonth(parsed);
            this.displayYear = getYear(parsed);
            this.generateCalendarDays();
        }
    }

    /**
     * Gets the dynamic classes.
     */
    get customClass() {
        const classes: Record<string, boolean> = {
            'bottom-20': this.position() === 'top' && this.hasError(),
            'bottom-14': this.position() === 'top',
            'mt-1': this.position() === 'bottom',
        };

        if (this.rightOffset() !== null) {
            classes[`right-${this.rightOffset()}`] = true;
        }

        if (this.customClasses()) {
            classes[this.customClasses()] = true;
        }

        return classes;
    }

    /**
     * Checks if a day is disabled based on current month the min and max dates
     * @param {CalendarDay} day - The day to check.
     */
    isDisabled(day: CalendarDay): boolean {
        const minDate = this.minDate();
        const maxDate = this.maxDate();

        if (!day.isCurrentMonth) {
            return true;
        }

        if (minDate && day.date < minDate) return true;
        if (maxDate && day.date > maxDate) return true;

        return false;
    }

    /**
     * Gets the error message.
     */
    get errorMessage() {
        const errors = this.ngControl?.control?.errors;
        if (!errors) return null;

        const errorKeys = Object.keys(errors);
        if (errorKeys.length > 0) {
            const firstErrorKey = errorKeys[0];
            return this.errorMessages()?.[firstErrorKey] ?? null;
        }
        return null;
    }

}
