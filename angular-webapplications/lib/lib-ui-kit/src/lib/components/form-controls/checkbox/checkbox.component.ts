/**
 * @fileoverview
 * A checkbox component that allows the user to select a value.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-22
 */
import { CommonModule } from '@angular/common';
import {
    Component,
    forwardRef,
    inject,
    Injector,
    input,
    OnInit,
    TemplateRef,
    ChangeDetectionStrategy
} from '@angular/core';
import {
    ControlValueAccessor,
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { isTemplateRef } from '../../../utils';

@Component({
    selector: 'lib-checkbox',
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './checkbox.component.html',
    styleUrl: './checkbox.component.css',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => CheckBoxComponent),
            multi: true,
        },
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CheckBoxComponent implements ControlValueAccessor, OnInit {
    id = input<string>('');
    name = input<string>('');
    label = input<string>('');
    required = input<boolean>(false);
    errorMessages = input<Record<string, string | TemplateRef<Component> | undefined>>();
    customClass = input<string | null>(null);
    checked = false;
    isDisabled = false;
    isTemplateRef = isTemplateRef;
    private ngControl: NgControl | null = null;
    private injector = inject(Injector);
    private onChange: (value: boolean) => void = () => { };
    private onTouched: () => void = () => { };

    ngOnInit(): void {
        const ngControl = this.injector.get(NgControl, null);
        if (ngControl) {
            this.ngControl = ngControl;
        }
    }

    /**
     * Checks if the checkbox is required.
     */
    get isRequired(): boolean {
        return (
            this.required() ||
            !!this.ngControl?.control?.hasValidator?.(Validators.required)
        );
    }

    /**
     * Writes the value to the checkbox.
     */
    writeValue(value: boolean): void {
        this.checked = !!value;
    }

    /**
     * Registers a change handler for the checkbox.
     */
    registerOnChange(fn: (value: boolean) => void): void {
        this.onChange = fn;
    }

    /**
     * Registers a touched handler for the checkbox.
     */
    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    /**
     * Sets the disabled state of the checkbox.
     */
    setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }

    /**
     * Handles the blur event for the checkbox.
     */
    onBlur(): void {
        this.onTouched();
    }

    /**
     * Checks if the checkbox has an error.
     */
    hasError(): boolean {
        return (
            !!this.ngControl?.control?.invalid &&
            !!this.ngControl.control.touched
        );
    }

    /**
     * Handles the change event for the checkbox.
     */
    onCheckboxChange(event: Event): void {
        const input = event.target as HTMLInputElement;
        this.checked = input.checked;
        this.onChange(this.checked);
    }

    /**
     * Gets the error message for the checkbox.
     */
    get errorMessage() {
        const errors = this.ngControl?.control?.errors;
        if (!errors) return null;

        const errorKeys = Object.keys(errors);
        if (errorKeys.length > 0) {
            const firstErrorKey = errorKeys[0];
            return this.errorMessages()?.[firstErrorKey] ?? null;
        }
        return null;
    }


    /**
     * Gets the custom classes for the checkbox.
     */
    get customClasses(): string {
        let className = '';
        if (this.hasError()) {
            className = 'border-none ring-1 ring-feedback-error';
        }
        if (this.customClass()) {
            className += ` ${this.customClass()}`;
        }
        return className;
    }
}
