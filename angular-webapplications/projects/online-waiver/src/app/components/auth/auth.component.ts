/**
 * @fileoverview Auth component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-17
 */

import { Component, inject, OnInit, signal } from '@angular/core';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { filter } from 'rxjs/operators';
import { PreviewWaiverComponent } from '../preview-waiver/preview-waiver.component';
import { HowToSignWaiverComponent } from '../how-to-sign-waiver/how-to-sign-waiver.component';
import { ModalComponent } from 'lib-ui-kit';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-auth',
    imports: [
        ModalComponent,
        HowToSignWaiverComponent,
        PreviewWaiverComponent,
        CommonModule,
        RouterModule,
    ],
    templateUrl: './auth.component.html',
    styleUrl: './auth.component.scss',
})
export class AuthComponent implements OnInit {
    private router = inject(Router);

    readonly showHowToSignWaivers = signal<boolean>(false);
    readonly showPreviewWaiver = signal<boolean>(false);
    activeTab: 'register' | 'login' = 'register';

    ngOnInit() {
        // Set active tab based on current route
        const currentUrl = this.router.url;
        if (currentUrl.includes('/auth/login')) {
            this.activeTab = 'login';
        } else if (currentUrl.includes('/auth/register')) {
            this.activeTab = 'register';
        }

        // Listen to route changes to update active tab
        this.router.events
            .pipe(filter((event) => event instanceof NavigationEnd))
            .subscribe((event: NavigationEnd) => {
                if (event.url.includes('/auth/login')) {
                    this.activeTab = 'login';
                } else if (event.url.includes('/auth/register')) {
                    this.activeTab = 'register';
                }
            });
    }

    setActiveTab(tab: 'register' | 'login'): void {
        this.activeTab = tab;
        if (tab === 'login') {
            this.router.navigate(['/auth/login']);
        } else {
            this.router.navigate(['/auth/register']);
        }
    }

    closeHowToSignWaivers() {
        this.showHowToSignWaivers.set(false);
    }

    closePreviewWaiver() {
        this.showPreviewWaiver.set(false);
    }
}
