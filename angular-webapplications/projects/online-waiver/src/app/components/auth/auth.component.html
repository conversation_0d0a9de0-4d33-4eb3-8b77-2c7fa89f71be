<div class="mx-auto max-w-7xl mb-32 md:mb-0">
    <!-- W<PERSON><PERSON> Header -->
    <div class="bg-surface-white rounded-4xl shadow-lg p-4 md:p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-xl font-semibold text-primary">Sign waivers</h1>
                <p class="text-sm text-primary mt-1">
                    Sign waivers to get access to the services.
                </p>
            </div>
            <div class="flex items-center gap-4 mt-4 md:mt-0">
                <button (click)="showHowToSignWaivers.set(true)"
                    class="flex items-center gap-1 text-sm text-secondary-blue underline">
                    <img src="assets/icons/close.svg" alt="Image" />
                    <span class="text-left"> How to sign waivers?</span>
                </button>
                <button (click)="showPreviewWaiver.set(true)"
                    class="flex items-center gap-1 text-sm text-secondary-blue underline">
                    <img src="assets/icons/preview-green.svg" alt="Image" />
                    <span class="text-left">Preview waiver</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Tabs and Forms -->
    <div>
        <!-- Tabs -->
        <div class="relative flex bg-surface-lightest rounded-t-4xl overflow-hidden w-full md:w-max">
            <!-- Register Tab -->
            <button (click)="setActiveTab('register')"
                class="relative text-primary flex flex-1 flex-col py-4 px-6 text-center w-max transition-all duration-150"
                [ngClass]="{
            'bg-surface-white text-secondary-blue -mb-[0.25rem] rounded-tl-[2rem] rounded-tr-[1.5rem] shadow-md after:content-[\'\'] after:absolute after:h-full after:w-8 after:bg-transparent after:bottom-[4px] after:right-[-2rem] after:rounded-bl-[1.5rem] after:transition-all after:duration-150 after:shadow-curve-after ':
              activeTab === 'register',
          }">
                <div class="text-xs text-nowrap text-center w-full">New customer?</div>
                <div class="font-semibold text-lg text-center w-full">Register</div>
            </button>

            <!-- Login Tab -->
            <button (click)="setActiveTab('login')"
                class="relative text-primary flex flex-1 flex-col py-4 px-6 text-center w-max transition-all duration-150"
                [ngClass]="{
            'bg-surface-white text-secondary-blue -mb-[0.25rem] rounded-tr-[2rem] rounded-tl-[1.5rem] shadow-md before:content-[\'\'] before:absolute before:h-full before:w-8 before:bg-transparent before:bottom-[4px] before:left-[-2rem] before:rounded-br-[1.5rem] before:transition-all before:duration-150 before:shadow-curve-before':
              activeTab === 'login',
          }">
                <div class="text-xs text-nowrap text-center w-full">
                    Existing customer?
                </div>
                <div class="font-semibold text-lg text-center w-full">Login</div>
            </button>
        </div>

        <div class="bg-surface-lightest rounded-b-4xl md:rounded-tr-4xl shadow-lg">
            <div class="bg-surface-white rounded-b-4xl md:rounded-tr-4xl overflow-hidden"
                [class.rounded-tl-0]="activeTab === 'register'" [class.rounded-tr-4xl]="activeTab === 'register'"
                [class.rounded-tl-4xl]="activeTab === 'login'">
                <!-- Form Container -->
                <div class="p-5 md:p-6 shadow-md">
                    <router-outlet></router-outlet>
                </div>
            </div>
        </div>
    </div>
</div>

<ng-template #howToSignWaiversContent> <app-how-to-sign-waiver /> </ng-template>

<lib-modal [isOpen]="showHowToSignWaivers()" [modalContent]="howToSignWaiversContent"
    (closeModal)="closeHowToSignWaivers()" dialogueHeader="How to sign waivers?">
</lib-modal>

<ng-template #showPreviewWaiverContent>
    <app-preview-waiver />
</ng-template>

<lib-modal [isOpen]="showPreviewWaiver()" [modalContent]="showPreviewWaiverContent" [isOpen]="showPreviewWaiver()"
    [modalContent]="showPreviewWaiverContent" (closeModal)="closePreviewWaiver()" dialogueHeader="How to sign waivers?">
</lib-modal>