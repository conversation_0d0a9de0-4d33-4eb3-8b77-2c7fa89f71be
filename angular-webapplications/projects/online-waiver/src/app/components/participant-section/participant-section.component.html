<section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-5">
    <div class="grid gap-2">
        <div class="flex flex-col gap-2">
            <h3 class="text-sm">{{ header() }}</h3>
            @if (description()) {
            <p class="text-sm text-neutral-dark">
                {{ description() }}
            </p>
            }
        </div>

        <div class="grid gap-2">
            @for (participant of participants(); track $index) {
            <app-participant-item [participant]="participant" (checkboxChange)="onParticipantCheckboxChange($event)" />
            @if (participants().length - 1 !== $index) {
            <hr class="text-surface hidden md:block" />
            }
            }
        </div>
    </div>
</section>