import { CommonModule } from '@angular/common';
import { Component, Signal, viewChildren } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { AddMinorFormComponent } from '../add-minor-form/add-minor-form.component';
import { RegistrationFormBaseComponent } from 'lib-auth';
import { DATE_FORMAT } from 'lib-ui-kit';

interface MinorFormData {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
}

@Component({
    selector: 'app-registration-form-waiver',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        AddMinorFormComponent,
        RegistrationFormBaseComponent
    ],
    templateUrl: './registration-form-waiver.component.html',
    styleUrl: './registration-form-waiver.component.css'
})
export class RegistrationFormWaiverComponent {

    minorFormComponents: Signal<readonly AddMinorFormComponent[]> = viewChildren(
        AddMinorFormComponent
    );

    protected readonly dateFormat = DATE_FORMAT;
    minors: MinorFormData[] = [];

    minorErrorMessages = {
        firstName: 'First name is required',
        lastName: 'Last name is required',
        dateOfBirth: 'Date of birth is required',
    };

    addMinor(): void {
        this.minors.push({
            firstName: '',
            lastName: '',
            dateOfBirth: '',
        });
    }

    removeMinor(index: number): void {
        this.minors.splice(index, 1);
    }
}


