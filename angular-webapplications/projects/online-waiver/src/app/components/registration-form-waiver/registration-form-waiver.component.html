<lib-registration-form-base>
    <div add-minor-form>
        @for (minorForm of minors; track $index) {
        <div>
            <app-add-minor-form [index]="$index" (remove)="removeMinor($index)"></app-add-minor-form>
        </div>
        }

        <!-- Add Minor Button -->
        <button type="button" (click)="addMinor()"
            class="w-full md:w-[30%] py-2 px-4 mb-6 bg-surface-white border border-primary rounded-full text-primary font-medium flex items-center justify-center gap-2">
            <img src="assets/icons/person-plus.svg" alt="Image" />
            Add Minor
        </button>
    </div>
</lib-registration-form-base>