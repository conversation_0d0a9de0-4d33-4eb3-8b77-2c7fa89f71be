<main class="max-w-[534px] mx-auto flex flex-col items-center mb-36 md:mb-0">
    <!-- Header Section -->
    <header
        class="md:px-6 md:pt-6 md:text-center md:bg-surface-white rounded-t-4xl"
    >
        <h1
            class="text-2xl font-semibold text-gray-900 mb-3 md:mb-6 leading-tight"
        >
            Welcome to Semnox waiver
        </h1>
        <p class="text-sm text-gray-500 leading-relaxed">
            To enjoy our services, each guest needs to sign a waiver. Don’t
            worry—it’s quick and easy! Just follow these simple steps:
        </p>
    </header>

    <!-- Steps Section -->
    <section
        class="w-full bg-white shadow-lg rounded-4xl md:rounded-t-none overflow-y-auto mt-5 md:mt-0"
    >
        <ol class="md:mt-6">
            @for (step of steps; track $index) {
            <li
                class="p-5 md:p-6 shadow-[0_-4px_10px_rgba(0,0,0,0.05)] rounded-t-4xl"
            >
                <article class="relative">
                    <span
                        class="block text-xs font-medium text-gray-500 py-0.5 rounded-xl border-2 border-white"
                    >
                        Step {{ $index + 1 }}
                    </span>
                    <div class="flex items-center gap-4 mt-1">
                        <img
                            [src]="step.icon"
                            [alt]="step.alt"
                            class="w-12 h-12"
                        />
                        <div class="flex-1">
                            <h2
                                class="mb-1 text-base font-semibold text-gray-900 leading-snug"
                            >
                                {{ step.title }}
                            </h2>
                            <p class="text-sm text-gray-500 leading-relaxed">
                                {{ step.description }}
                            </p>
                        </div>
                    </div>
                </article>
            </li>
            }
        </ol>

        <!-- Desktop CTA -->
        <footer class="py-6 hidden md:flex flex-col items-center">
            <ng-container *ngTemplateOutlet="ctaTemplate"></ng-container>
        </footer>
    </section>

    <!-- Mobile CTA -->
    <lib-page-footer>
        <ng-container *ngTemplateOutlet="ctaTemplate"></ng-container>
    </lib-page-footer>
</main>

<!-- Shared CTA Template -->
<ng-template #ctaTemplate>
    <button
        type="button"
        (click)="onRegisterClick()"
        class="w-full md:max-w-[315px] py-4 px-6 mb-4 font-medium text-white bg-gray-800 rounded-4xl"
    >
        Register and sign waiver
    </button>
    <p class="text-sm text-center text-gray-500">
        Already have an account?
        <button
            type="button"
            (click)="onLoginClick()"
            class="font-medium text-blue-500 hover:underline"
        >
            Login
        </button>
    </p>
</ng-template>
