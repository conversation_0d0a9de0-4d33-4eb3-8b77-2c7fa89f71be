/**
 * @fileoverview Welcome to waivers component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-17
 */
import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { PageFooterComponent } from 'lib-ui-kit';

@Component({
    selector: 'app-welcome-to-waivers',
    imports: [PageFooterComponent, CommonModule],
    templateUrl: './welcome-to-waivers.component.html',
    styleUrl: './welcome-to-waivers.component.scss',
})
export class WelcomeToWaiversComponent {
    private router = inject(Router);
    steps = [
        {
            icon: '/assets/icons/user-check.svg',
            alt: 'login-icon',
            title: 'Login to your account',
            description: 'Log in to your account or register to get started.',
        },
        {
            icon: '/assets/icons/sign.svg',
            alt: 'sign-icon',
            title: 'Select the waiver and sign',
            description:
                'Sign for yourself and your dependents (family & friends) to receive waiver acknowledgment.',
        },
        {
            icon: '/assets/icons/check-in.svg',
            alt: 'check-in-icon',
            title: 'Check-in online',
            description:
                'Enter the code/OTP received during waiver signing and proceed',
        },
    ];

    onRegisterClick() {
        this.router.navigate(['/auth/register']);
    }

    onLoginClick() {
        this.router.navigate(['/auth/login']);
    }
}
