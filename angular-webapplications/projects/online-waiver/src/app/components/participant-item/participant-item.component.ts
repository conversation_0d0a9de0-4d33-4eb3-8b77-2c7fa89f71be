/**
 * @fileoverview ParticipantItemComponent is a component that displays a participant item
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { Component, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CheckBoxComponent } from 'lib-ui-kit';
import { TagComponent } from '../tag/tag.component';
import { ParticipantItem } from '../../interfaces/customer.interface';

@Component({
  selector: 'app-participant-item',
  standalone: true,
  imports: [CommonModule, CheckBoxComponent, TagComponent],
  templateUrl: './participant-item.component.html',
  styles: []
})
export class ParticipantItemComponent {
  participant = input.required<ParticipantItem>();
  checkboxChange = output<{ participant: ParticipantItem; checked: boolean }>();

  onCheckboxChange(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.checkboxChange.emit({ participant: this.participant(), checked: checkbox.checked });
  }
} 