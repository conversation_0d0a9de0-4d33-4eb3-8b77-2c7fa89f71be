<div class="flex items-center justify-between pr-[11px]">
    <div class="flex items-center justify-center gap-3">
        <lib-checkbox customClass="w-[18px] h-[18px]" (change)="onCheckboxChange($event)" />
        <div class="flex flex-col">
            <p class="text-sm">
                {{ participant().firstName + ' ' + participant().lastName }}
            </p>
            <div class="text-sm text-neutral-dark">
                {{ participant().dateOfBirth | date:'dd-MM-yyyy' }}
            </div>
        </div>
    </div>
    @if (participant().tag) {
    <app-tag [label]="participant().tag!.label" [severity]="participant().tag!.severity" />
    }
</div>