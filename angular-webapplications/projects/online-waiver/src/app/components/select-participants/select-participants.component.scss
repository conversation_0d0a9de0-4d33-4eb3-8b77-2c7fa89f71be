/* Hide the default checkbox */
.custom-checkbox {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--color-secondary-blue); /* Blue border */
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--color-surface-white);
  }
  
  /* Custom checkmark using :checked */
  .custom-checkbox:checked {
    background-color: var(--color-secondary-blue);
    border-color: var(--color-secondary-blue);
  }
  
  .custom-checkbox:checked::after {
    content: '';
    background-image: url('/assets/icons/checkbox-checked.svg');
    background-size: cover;
    width: 100%;
    height: 100%;
    display: block;
  }
  