<div class="grid gap-6 max-w-7xl mx-auto mb-32 md:mb-0">
    <h1 class="text-lg font-semibold">{{ text.PAGE_TITLE }}</h1>

    <!-- Participants Section -->
    <section class="bg-surface-white rounded-4xl p-2 md:p-6 grid gap-2 md:gap-6">
        <h2 class="text-lg font-medium text-primary px-3 pt-3 md:p-0 mb-1.5 md:mb-0">
            {{ text.SECTION_TITLE }}
        </h2>

        <!-- Primary Account Holder -->
        @if (primaryParticipant$ | async; as primaryState) {
        @if (primaryState.loading) {
        <section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-2">
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div class="h-6 bg-gray-200 rounded w-3/4"></div>
            </div>
        </section>
        } @else if (primaryState.error) {
        <section class="p-5 bg-red-50 rounded-3xl flex flex-col gap-2">
            <p class="text-red-600 text-sm">{{ primaryState.error }}</p>
        </section>
        } @else if (primaryState.data) {
        <app-participant-section [header]="text.PRIMARY_ACCOUNT_HOLDER"
            [participants]="[convertToParticipantItem(primaryState.data!)]"
            (participantChange)="onParticipantChange($event)" />
        }
        }

        <!-- Minor Participants -->
        @if (minorParticipants$ | async; as minorState) {
        @if (minorState.loading) {
        <section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-5">
            <div class="animate-pulse space-y-2">
                <div class="h-12 bg-gray-200 rounded"></div>
                <div class="h-12 bg-gray-200 rounded"></div>
            </div>
        </section>
        } @else if (minorState.error) {
        <section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-5">
            <div class="p-3 bg-red-50 rounded">
                <p class="text-red-600 text-sm">{{ minorState.error }}</p>
            </div>
        </section>
        } @else if (minorState.data) {
        <app-participant-section [header]="text.MINORS" [description]="text.MINORS_DESCRIPTION"
            [participants]="processMinorParticipants(minorState.data)"
            (participantChange)="onParticipantChange($event)" />
        }
        }

        <!-- Add Minor Button -->
        <button type="button" (click)="addMinor()"
            class="w-full md:w-[30%] py-2 px-4 mb-6 bg-transparent border border-primary rounded-full text-primary font-medium flex items-center justify-center gap-2">
            <img src="assets/icons/person-plus.svg" alt="Image" />
            {{ text.ADD_MINOR }}
        </button>

        <hr class="text-surface hidden md:block" />

        <!-- Desktop CTA -->
        <div class="hidden md:flex flex-col md:flex-row md:items-center gap-4">
            <!-- Login Button -->
            <button
                class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface"
                (click)="signWaiver()">
                {{ text.SIGN_WAIVER }}
            </button>
            <div class="text-sm text-center md:text-right">
                {{ text.ALREADY_HAVE_ACCOUNT }}
                <a href="#" class="text-blue-600 underline">{{ text.LOGIN }}</a>
            </div>
        </div>
    </section>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <div class="flex flex-col justify-center items-center gap-4">
        <!-- Login Button -->
        <button class="w-full max-w-[23rem] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface"
            (click)="signWaiver()">
            {{ text.SIGN_WAIVER }}
        </button>
        <div class="text-sm text-center md:text-right">
            {{ text.ALREADY_HAVE_ACCOUNT }}
            <a href="#" class="text-blue-600 underline">{{ text.LOGIN }}</a>
        </div>
    </div>
</lib-page-footer>