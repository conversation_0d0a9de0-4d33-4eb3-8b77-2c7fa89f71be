/**
 * @fileoverview SelectParticipantsComponent is a component that displays a select participants component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
/**
 * @component SelectParticipantsComponent
 * @description
 * Displays Primary and related customer details such as firstName, lastName, DOB and signed status
 * Allows Adding the new minor and select the participants for signing
 * Also updates the component based on waiver signed and minors added
 *
 * @usage
 * User will be routed to this page with CustomerId and SelectedWaiverSetId as a route params
 *
 * @inputs
 * - CustomerId  : Logged in primary customer Id
 * - SelectedWaiverSetId : The waiver set selected in landing page multi waiver scenario , if only one waiver is configured by default it is considered that ID
 *
 * @outputs
 * - Redirects to sign waiver screen with selected list of customerId's
 *
 * @dependencies
 * - SignWaiverService: Handles API communication related to customer data.
 *
 * @methods
 * - initiateAddParticipants(): Communicates with api service and processes the user data for display.
 * - addMinor(): opens the add minor form to enter the minor details.
 * - saveMinor(): saves the values enetered in add minor form.
 * - signWaiver(): Redirects to sign waiver component with selected list of customerIds
 *
 *  @Tobe_saved
 *  waiverResp , signed waivers resp
 */

import {
    Component,
    inject,
    signal,
    viewChildren,
    computed,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AsyncPipe } from '@angular/common';
import { AddMinorFormComponent } from '../add-minor-form/add-minor-form.component';
import { PageFooterComponent } from 'lib-ui-kit';
import { Router } from '@angular/router';
import { CustomerRelationBL } from '../../services/business-layer/customer-relation-bl.service';
import { RelatedCustomerDL } from '../../services/data-layer/related-customer-dl.service';
import { PrimaryCustomerDL } from '../../services/data-layer/primary-customer-dl.service';
import {
    IParticipant,
    ParticipantItem,
} from '../../interfaces/customer.interface';
import { ParticipantSectionComponent } from '../participant-section/participant-section.component';
import { selectParticipantsText } from '../../constants/customer.constant';

@Component({
    selector: 'app-select-participants',
    imports: [
        ReactiveFormsModule,
        FormsModule,
        AsyncPipe,
        PageFooterComponent,
        ParticipantSectionComponent,
    ],
    templateUrl: './select-participants.component.html',
    providers: [CustomerRelationBL, RelatedCustomerDL, PrimaryCustomerDL],
    styleUrl: './select-participants.component.scss',
})
export class SelectParticipantsComponent {
    readonly signInCode = signal<string>('SJ5FK0');
    selectedParticipants = signal<IParticipant[]>([]);
    newMinors = signal<IParticipant[]>([]);
    minorFormComponents = viewChildren(AddMinorFormComponent);
    private _customerRelationBL = inject(CustomerRelationBL);
    private _router = inject(Router);
    readonly text = selectParticipantsText;

    // Use observables directly for async pipe compatibility
    primaryParticipant$ = this._customerRelationBL.getPrimaryCustomerData();
    minorParticipants$ = this._customerRelationBL.getRelatedCustomerData();

    minorErrorMessages = {
        firstName: 'First name is required',
        lastName: 'Last name is required',
        dateOfBirth: 'Date of birth is required',
    };

    //Signs the waiver
    //This method is used to sign the waiver
    signWaiver() {
        const minorFormData = this.minorFormComponents().map(
            (minorFormComponent) => minorFormComponent.getFormData()
        );
        this._router.navigate(['/waivers/sign-waiver/53/1']);
    }

    //Adds a minor
    //This method is used to add a minor
    addMinor(): void {
        this.newMinors.update((prev) => [
            ...prev,
            { firstName: '', lastName: '', dateOfBirth: '', type: 'minor' },
        ]);
    }

    //Removes a minor
    //This method is used to remove a minor
    removeMinor(index: number): void {
        this.newMinors.update((prev) => prev.filter((_, i) => i !== index));
    }

    //Checks if a minor is valid
    //This method is used to check if a minor is valid
    isMinorValid(minor: IParticipant) {
        return (
            minor.firstName &&
            minor.lastName &&
            minor.dateOfBirth &&
            minor.type === 'minor'
        );
    }

    /**
     * This function is called when the checkbox is changed.
     * It adds or removes the participant from the selected participants list.
     * @param participant - The participant that is being selected or deselected.
     * @param event - The event that is being triggered.
     */
    onCheckboxChange(participant: IParticipant, event: Event): void {
        const checkbox = event.target as HTMLInputElement;
        if (checkbox.checked) {
            this.selectedParticipants.update((prev) => [...prev, participant]);
        } else {
            this.selectedParticipants.update((prev) =>
                prev.filter((p) => p !== participant)
            );
        }
    }

    /**
     * Handles participant checkbox changes from the new components
     */
    onParticipantChange(event: {
        participant: ParticipantItem;
        checked: boolean;
    }): void {
        const participant = event.participant as IParticipant;
        if (event.checked) {
            this.selectedParticipants.update((prev) => [...prev, participant]);
        } else {
            this.selectedParticipants.update((prev) =>
                prev.filter((p) => p !== participant)
            );
        }
    }

    /**
     * Converts IParticipant to ParticipantItem with tag
     */
    convertToParticipantItem(participant: IParticipant): ParticipantItem {
        return {
            ...participant,
            tag: participant.isSigned
                ? { label: 'Signed', severity: 'success' as const }
                : { label: 'Not Signed', severity: 'error' as const },
        };
    }

    /**
     * Processes minor participants data for the component
     */
    processMinorParticipants(minors: IParticipant[]): ParticipantItem[] {
        return minors
            .filter((minor) => this.isMinorValid(minor))
            .map((minor) => this.convertToParticipantItem(minor));
    }
}
