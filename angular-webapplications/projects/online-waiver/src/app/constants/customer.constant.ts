export const customerValidation = {
    CUSTOMER_RELATION_ERROR: 'Invalid response format',
    PRIMARY_CUSTOMER_ERROR: 'Failed to load primary customer data',
    RELATED_CUSTOMER_ERROR: 'Failed to load related customer data',
    NO_PRIMARY_CUSTOMER_DATA: 'No primary customer data found',
    ERROR_LOADING_PRIMARY_CUSTOMER_DATA: 'Error loading primary customer data:',
    ERROR_LOADING_RELATED_CUSTOMER_DATA: 'Error loading related customer data:',
};

export const selectParticipantsText = {
    PAGE_TITLE: 'Select participants',
    SECTION_TITLE: 'Select members to sign',
    PRIMARY_ACCOUNT_HOLDER: 'Primary account holder',
    MINORS: 'Minors',
    MINORS_DESCRIPTION: 'Minors info should be added in order to selecting them for signing.',
    ADD_MINOR: 'Add Minor',
    SIGN_WAIVER: 'Sign Waiver',
    ALREADY_HAVE_ACCOUNT: 'Already have an account?',
    LOGIN: 'Login'
};