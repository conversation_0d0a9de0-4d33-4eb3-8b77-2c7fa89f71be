import { BaseDTO } from "lib/lib-app-core/src/lib/models/base-dto.model";
import { CustomerDTO } from "./customer-data-dto.model";

export class CustomerRelationshipDTO extends BaseDTO<CustomerRelationshipDTO> {
    Id!: number;
    CustomerId!: number;
    CustomerName!: string;
    RelatedCustomerId!: number;
    RelatedCustomerName!: string;
    CustomerRelationshipTypeId!: number;
    EffectiveDate!: string | null;
    ExpiryDate!: string | null;
    IsActive!: boolean;
    CreatedBy!: string;
    CreationDate!: string;
    LastUpdatedBy!: string;
    LastUpdateDate!: string;
    SiteId!: number;
    MasterEntityId!: number;
    SynchStatus!: boolean;
    Guid!: string;
    CustomerDTO!: CustomerDTO;
    RelatedCustomerDTO!: CustomerDTO;
    IsChanged!: boolean;
}