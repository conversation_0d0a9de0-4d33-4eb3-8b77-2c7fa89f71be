import { BaseDTO } from "lib/lib-app-core/src/lib/models/base-dto.model";
import { CustomerSignedWaiverDTO } from "./customer-signed-dto.model";
import { UserAddressDTOModel } from "lib/lib-auth/src/lib/models/user-address-dto.model";
import { UserContactDTOModel } from "lib/lib-auth/src/lib/models/user-contact-dto.model";
import { UserProfileDTOModel } from "lib/lib-auth/src/lib/models/user-profile-dto.model";
import { CustomDataSetDTOModel } from "lib/lib-auth/src/lib/models/custom-data-set-dto.model";

export class CustomerDTO extends BaseDTO<CustomerDTO>{
    Id!: number;
    IsActive!: boolean;
    IsBonusLoaded!: boolean;
    ProfileId!: number;
    MembershipId!: number;
    Title!: string;
    FirstName!: string;
    NickName!: string;
    MiddleName!: string;
    LastName!: string;
    ExternalSystemReference!: string;
    CustomerType!: number;
    UniqueIdentifier!: string;
    TaxCode!: string;
    DateOfBirth!: string;
    Gender!: string;
    Anniversary!: string | null;
    TeamUser!: boolean;
    RightHanded!: boolean;
    OptInPromotions!: boolean;
    OptInPromotionsMode!: string;
    PolicyTermsAccepted!: boolean;
    Company!: string;
    UserName!: string;
    PhotoURL!: string;
    IdProofFileURL!: string;
    LastLoginTime!: string | null;
    Designation!: string;
    CustomDataSetId!: number;
    Notes!: string;
    CardNumber!: string;
    Channel!: string;
    Verified!: boolean;
    AddressDTOList!: UserAddressDTOModel[] | null;
    ContactDTOList!: UserContactDTOModel[];
    ProfileDTO!: UserProfileDTOModel;
    CustomerVerificationDTO!: any;
    CustomDataSetDTO!: CustomDataSetDTOModel;
    CreatedBy!: string;
    CreationDate!: string;
    LastUpdatedBy!: string;
    LastUpdateDate!: string;
    SiteId!: number;
    MasterEntityId!: number;
    SynchStatus!: boolean;
    Guid!: string;
    PhoneNumber!: string;
    PhoneContactDTO!: UserContactDTOModel;
    Password!: string;
    LatestAddressDTO!: UserAddressDTOModel;
    SecondaryPhoneNumber!: string;
    FBUserId!: string;
    FBAccessToken!: string;
    TWAccessToken!: string;
    TWAccessSecret!: string;
    Email!: string;
    WeChatAccessToken!: string;
    IsChanged!: boolean;
    IsChangedRecursive!: boolean;
    CustomerCuponsDT!: any;
    AccountDTOList!: any[];
    CustomerMembershipProgressionDTOList!: any[];
    CustomerMembershipRewardsLogDTOList!: any[];
    CustomerSignedWaiverDTOList!: CustomerSignedWaiverDTO[];
    CustomerApprovalLogDTOList!: any[];
    ActiveCampaignCustomerInfoDTOList!: any[];
    LastVisitedDate!: string;
    Status!: string;
    StatusId!: number;
    StatusChangeDate!: string;
  }