import { BaseDTO } from 'lib/lib-app-core/src/lib/models/base-dto.model';

export class CustomerSignedWaiverDTO extends BaseDTO<CustomerSignedWaiverDTO> {
    CustomerSignedWaiverId!: number;
    CustomerSignedWaiverHeaderId!: number;
    WaiverSetDetailId!: number;
    SignedWaiverFileName!: string;
    WaiverName!: string;
    WaiverFileName!: string;
    SignedFor!: number;
    SignedForName!: string;
    ExpiryDate!: string | null;
    IsActive!: boolean;
    DeactivatedBy!: string | null;
    DeactivationDate!: string | null;
    DeactivationApprovedBy!: string | null;
    Guid!: string;
    SynchStatus!: boolean;
    MasterEntityId!: number;
    CreatedBy!: string;
    CreationDate!: string;
    LastUpdatedBy!: string;
    LastUpdateDate!: string;
    SiteId!: number;
    WaiverSignedImageList!: any[];
    CustomerContentForWaiverDTOList!: any[];
    SignedBy!: number;
    SignedByName!: string;
    SignedDate!: string;
    WaiverCode!: string;
    WaiverSetId!: number;
    WaiverSetDescription!: string;
    SignedWaiverFileContentInBase64Format!: string | null;
    GuardianId!: number;
    IsChangedRecursive!: boolean;
    IsChanged!: boolean;
}