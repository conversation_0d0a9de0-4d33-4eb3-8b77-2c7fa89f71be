import { UserLoginDTOModel } from "lib/lib-auth/src/lib/models/user-login-dto.model";
import { CustomerRelationshipDTO } from "../models/customer-relationship-dto.model";

export interface CustomerAPIParams {
    customerId: string;
}

export interface RelatedCustomerResponse {
    CustomerRelationshipDTO?: CustomerRelationshipDTO[];
}

export interface CustomerDataResponse {
    data?: UserLoginDTOModel[];
    customersImage?: string;
    customersIdImage?: string;
    totalPages?: number;
}

export interface ILoadingState<T> {
    loading: boolean;
    data: T | null;
    error: string | null;
}

export interface IParticipant {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    type: 'primary' | 'minor';
    isSigned?: boolean;
}

export interface ParticipantItem {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    type: 'primary' | 'minor';
    isSigned?: boolean;
    tag?: {
        label: string;
        severity: 'success' | 'error' | 'warning' | 'info';
    };
}