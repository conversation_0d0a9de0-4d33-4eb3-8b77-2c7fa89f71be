<div class="fixed z-40 lg:hidden" [ngClass]="{
    'inset-0': isOpen(),
    'left-[-100%]': !isOpen(),
  }">
    <div #sidebarBackdrop class="absolute inset-0 bg-modal-overlay"
        (click)="closeMobileSidebar(); $event.stopPropagation()" tabindex="0" role="button"
        (keydown.enter)="closeMobileSidebar(); $event.stopPropagation()"
        (keydown.space)="closeMobileSidebar(); $event.stopPropagation()"></div>
    <div #mobileSidebarContainer
        class="fixed inset-y-0 left-0 mt-[60px] max-w-xs w-full bg-surface-lighter transform transition-transform ease-in-out overflow-y-auto"
        [class.translate-x-0]="isOpen()" [class.translate-x-[-100%]]="!isOpen()">
        <!-- Menu Items -->
        <div class="p-5 space-y-2">
            @for (item of menuItems; track $index) {
            <ng-container>
                <!-- Main Menu Item -->
                <div class="rounded-xl overflow-hidden bg-surface-white shadow-sm">
                    <div class="flex items-center justify-between p-5" [class.cursor-pointer]="item.children"
                        (click)="item.children ? toggleSubmenu(item) : null" tabindex="0" role="button"
                        (keydown.enter)="item.children ? toggleSubmenu(item) : null"
                        (keydown.space)="item.children ? toggleSubmenu(item) : null">
                        @if (!item.children) {
                        <a [routerLink]="item.link" class="flex items-center flex-grow"
                            (click)="closeMobileSidebar(); $event.stopPropagation()">
                            <img [src]="item.icon" [alt]="item.label" class="w-6 h-6 mr-3" />
                            <span class="text-primary font-medium">{{ item.label }}</span>
                        </a>
                        } @else {
                        <div class="flex items-center flex-grow">
                            <img [src]="item.icon" [alt]="item.label" class="w-6 h-6 mr-3" />
                            <span class="text-primary font-medium">{{ item.label }}</span>
                        </div>
                        }
                        @if (item.children) {
                        <img src="/assets/icons/chevron-down.svg" alt="Expand" class="w-5 h-5 transition-transform"
                            [class.transform]="item.isExpanded" [class.rotate-180]="item.isExpanded" />
                        }
                    </div>

                    <!-- Submenu Items -->
                    @if (item.children && item.isExpanded) {
                    <div class="flex flex-col gap-y-1 pb-4">
                        @for (child of item.children; track $index) {
                        <ng-container>
                            @if (child.action) {
                            <button (click)="child.action(); closeMobileSidebar(); $event.stopPropagation()"
                                class="block mx-4 py-1.5 pl-12 text-sm text-primary hover:bg-surface-lighter rounded-lg w-full text-left"
                                [class.text-secondary-blue]="child.label === 'My Cards'">
                                {{ child.label }}
                            </button>
                            } @else {
                            <a [routerLink]="child.link" routerLinkActive="text-secondary-blue"
                                (click)="closeMobileSidebar(); $event.stopPropagation()"
                                class="block mx-4 py-1.5 pl-12 text-sm text-primary hover:bg-surface-lighter rounded-lg"
                                [class.text-secondary-blue]="child.label === 'My Cards'">
                                {{ child.label }}
                            </a>
                            }
                        </ng-container>
                        }
                    </div>
                    }
                </div>
            </ng-container>
            }

            <!-- Logout Button -->
            <button (click)="logout()" class="flex items-center w-full text-sm px-5 py-1.5 text-feedback-error">
                <img src="/assets/icons/logout.svg" alt="Logout" class="mr-3" />
                <span class="font-medium">Logout</span>
            </button>
        </div>
    </div>
</div>