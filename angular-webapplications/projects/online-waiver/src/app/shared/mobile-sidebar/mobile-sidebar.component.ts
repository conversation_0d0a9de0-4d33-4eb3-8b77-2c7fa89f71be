import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
    Component,
    HostListener,
    Inject,
    input,
    output,
    PLATFORM_ID,
    inject,
} from '@angular/core';
import { RouterLink, RouterLinkActive, Router } from '@angular/router';
import { CookieService } from 'lib-app-core';
import { AuthService } from '../../core/auth.service';

interface Language {
    code: string;
    name: string;
    icon: string;
}

interface MenuItem {
    label: string;
    icon: string;
    link?: string;
    action?: () => void;
    children?: MenuItem[];
    isExpanded?: boolean;
}

@Component({
    selector: 'app-mobile-sidebar',
    imports: [CommonModule, RouterLink, RouterLinkActive],
    templateUrl: './mobile-sidebar.component.html',
    styleUrl: './mobile-sidebar.component.scss',
})
export class MobileSidebarComponent {
    isOpen = input(false);
    closeSidebar = output<void>();
    private readonly MOBILE_BREAKPOINT = 1024;

    isBrowser = false;
    private router = inject(Router);
    private cookieService = inject(CookieService);
    private authService = inject(AuthService);

    constructor(@Inject(PLATFORM_ID) private platformId: Object) {
        this.isBrowser = isPlatformBrowser(this.platformId);
    }

    // HostListener for window resize
    @HostListener('window:resize', ['$event'])
    onResize() {
        if (this.isBrowser) {
            if (window.innerWidth < this.MOBILE_BREAKPOINT && this.isOpen()) {
                this.closeSidebar.emit();
            }
        }
    }

    menuItems: MenuItem[] = [
        {
            label: 'Home',
            icon: '/assets/icons/home.svg',
            link: '/home',
        },
        {
            label: 'Cards',
            icon: '/assets/icons/card.svg',
            link: '/cards',
        },
        {
            label: 'Waivers',
            icon: '/assets/icons/preview-blue.svg',
            link: '/waivers',
            isExpanded: false,
            children: [
                {
                    label: 'Sign Waiver',
                    icon: '',
                    link: '/waivers/sign-waiver',
                },
                {
                    label: 'View Signed Waivers',
                    icon: '',
                    link: '/waivers/my-signed-waivers',
                },
                {
                    label: 'View Relations',
                    icon: '',
                    link: '/waivers/my-relations',
                },
            ],
        },
        {
            label: 'Food & Beverages',
            icon: '/assets/icons/food-beverages.svg',
            link: '/f-and-b',
        },
        {
            label: 'Recharge',
            icon: '/assets/icons/recharge.svg',
            link: '/recharge',
        },
        {
            label: 'Party Reservations',
            icon: '/assets/icons/reservation.svg',
            link: '/reservations',
        },
        {
            label: 'My Account',
            icon: '/assets/icons/account.svg',
            isExpanded: true,
            children: [
                {
                    label: 'My Profile',
                    icon: '',
                    link: 'my-accounts/my-profile',
                },
                { label: 'My Cards', icon: '', link: 'my-accounts/my-cards' },
                {
                    label: 'My Subscriptions',
                    icon: '',
                    link: 'my-accounts/my-subscriptions',
                },
                { label: 'My Orders', icon: '', link: 'my-accounts/my-orders' },
                {
                    label: 'Change Password',
                    icon: '',
                    link: 'my-accounts/change-password',
                },
                {
                    label: 'Logout',
                    icon: '',
                    action: () => this.logout(),
                },
            ],
        },
    ];

    languages = [
        { code: 'EN', name: 'English', icon: '/assets/images/flags/en.png' },
        { code: 'ES', name: 'Spanish', icon: '/assets/images/flags/es.png' },
        { code: 'FR', name: 'French', icon: '/assets/images/flags/fr.png' },
    ];

    selectedLanguage = this.languages[0];
    showLanguageDropdown = false;

    toggleSubmenu(item: MenuItem): void {
        item.isExpanded = !item.isExpanded;
    }

    toggleLanguageDropdown(): void {
        this.showLanguageDropdown = !this.showLanguageDropdown;
    }

    selectLanguage(language: Language): void {
        this.selectedLanguage = language;
        this.showLanguageDropdown = false;
    }

    closeMobileSidebar(): void {
        if (this.isOpen()) {
            this.closeSidebar.emit();
        }
    }

    logout(): void {
        // Use the shared auth service
        this.authService.logout();

        // Close the mobile sidebar if it's open
        if (this.isOpen()) {
            this.closeSidebar.emit();
        }
    }
}
