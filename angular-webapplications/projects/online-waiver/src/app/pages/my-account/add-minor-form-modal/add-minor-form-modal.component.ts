import { Component } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DATE_FORMAT, DatePickerComponent, ErrorMessage, TextInputComponent } from 'lib-ui-kit';

interface MinorFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
}

@Component({
    selector: 'app-add-minor-form-modal',
    imports: [TextInputComponent, DatePickerComponent, ReactiveFormsModule],
    templateUrl: './add-minor-form-modal.component.html',
    styleUrl: './add-minor-form-modal.component.scss'
})
export class AddMinorFormModalComponent {
    minorForm: FormGroup;
    protected readonly dateFormat = DATE_FORMAT;

    errorMessages: ErrorMessage<MinorFormData> = {
        firstName: {
            required: 'First name is required',
        },
        lastName: {
            required: 'Last name is required',
        },
        dateOfBirth: {
            required: 'Date of birth is required',
        },
    };


    constructor(private fb: FormBuilder) {
        this.minorForm = this.fb.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            dateOfBirth: ['', Validators.required],
        });
    }

    onSubmit() {
        if (this.minorForm.valid) {
            console.log('Form submitted', this.minorForm.value);
        }
    }

}
