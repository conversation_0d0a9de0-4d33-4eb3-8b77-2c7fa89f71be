{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"online-waiver": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/online-waiver", "sourceRoot": "projects/online-waiver/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/online-waiver", "index": "projects/online-waiver/src/index.html", "browser": "projects/online-waiver/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/online-waiver/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/online-waiver/public"}, {"glob": "**/*", "input": "./data", "output": "/data"}], "styles": ["projects/online-waiver/src/styles.scss"], "scripts": [], "server": "projects/online-waiver/src/main.server.ts", "prerender": true, "ssr": {"entry": "projects/online-waiver/src/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "online-waiver:build:production"}, "development": {"buildTarget": "online-waiver:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/online-waiver/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/online-waiver/public"}], "styles": ["projects/online-waiver/src/styles.scss"], "scripts": []}}}}, "lib-app-core": {"projectType": "library", "root": "lib/lib-app-core", "sourceRoot": "lib/lib-app-core/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "lib/lib-app-core/ng-package.json"}, "configurations": {"production": {"tsConfig": "lib/lib-app-core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "lib/lib-app-core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "lib/lib-app-core/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}, "lib-auth": {"projectType": "library", "root": "lib/lib-auth", "sourceRoot": "lib/lib-auth/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "lib/lib-auth/ng-package.json"}, "configurations": {"production": {"tsConfig": "lib/lib-auth/tsconfig.lib.prod.json"}, "development": {"tsConfig": "lib/lib-auth/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "lib/lib-auth/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}, "lib-ui-kit": {"projectType": "library", "root": "lib/lib-ui-kit", "sourceRoot": "lib/lib-ui-kit/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "lib/lib-ui-kit/ng-package.json"}, "configurations": {"production": {"tsConfig": "lib/lib-ui-kit/tsconfig.lib.prod.json"}, "development": {"tsConfig": "lib/lib-ui-kit/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "lib/lib-ui-kit/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}, "sandbox-lab": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/sandbox-lab", "sourceRoot": "projects/sandbox-lab/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/sandbox-lab", "index": "projects/sandbox-lab/src/index.html", "browser": "projects/sandbox-lab/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/sandbox-lab/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/sandbox-lab/public"}], "styles": ["projects/sandbox-lab/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "sandbox-lab:build:production"}, "development": {"buildTarget": "sandbox-lab:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/sandbox-lab/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/sandbox-lab/public"}], "styles": ["projects/sandbox-lab/src/styles.scss"], "scripts": []}}}}, "lib-my-account": {"projectType": "library", "root": "lib/lib-my-account", "sourceRoot": "lib/lib-my-account/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "lib/lib-my-account/ng-package.json"}, "configurations": {"production": {"tsConfig": "lib/lib-my-account/tsconfig.lib.prod.json"}, "development": {"tsConfig": "lib/lib-my-account/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "lib/lib-my-account/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}}, "cli": {"analytics": false}}