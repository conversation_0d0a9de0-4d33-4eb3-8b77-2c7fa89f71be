{"data": {"CustomerFieldConfigurationContainerDTOList": [{"FieldName": "CUSTOMER_NAME", "Description": "Show CUSTOMER_NAME", "EntityFieldName": "ProfileDTO.FirstName", "Primarygroup": "Primary", "SubGroup": "", "FieldOrder": "1", "FieldLabel": "First Name", "FieldType": "TEXT", "CustomeAttributeId": -1, "FixedFieldAttributeId": -1, "DisplayFormat": "", "DefaultValue": "", "IsReadOnly": false, "IsDisplayed": true, "IsMandatory": true, "IsUnique": false, "IsCompositeUnique": false, "MinLength": -1, "MaxLength": 50, "SpecificLength": "-1", "ValidationType": "NONE", "RegexValue": "", "CustomerFieldValueList": []}, {"FieldName": "LAST_NAME", "Description": "Show LAST_NAME", "EntityFieldName": "ProfileDTO.LastName", "Primarygroup": "Primary", "SubGroup": "", "FieldOrder": "2", "FieldLabel": "Last Name", "FieldType": "TEXT", "CustomeAttributeId": -1, "FixedFieldAttributeId": -1, "DisplayFormat": "", "DefaultValue": "", "IsReadOnly": false, "IsDisplayed": true, "IsMandatory": false, "IsUnique": false, "IsCompositeUnique": false, "MinLength": -1, "MaxLength": 50, "SpecificLength": "-1", "ValidationType": "NONE", "RegexValue": "", "CustomerFieldValueList": []}, {"FieldName": "BIRTH_DATE", "Description": "Show BIRTH_DATE", "EntityFieldName": "ProfileDTO.DateOfBirth", "Primarygroup": "Primary", "SubGroup": "", "FieldOrder": "3", "FieldLabel": "Birth Date", "FieldType": "DATE", "CustomeAttributeId": -1, "FixedFieldAttributeId": -1, "DisplayFormat": "MM-dd-yyyy", "DefaultValue": "", "IsReadOnly": false, "IsDisplayed": true, "IsMandatory": false, "IsUnique": false, "IsCompositeUnique": false, "MinLength": -1, "MaxLength": -1, "SpecificLength": "-1", "ValidationType": "NONE", "RegexValue": "", "CustomerFieldValueList": []}, {"FieldName": "CONTACT_PHONE", "Description": "Show Contact Phone", "EntityFieldName": "Attribute1", "Primarygroup": "Contact", "SubGroup": "Phone", "FieldOrder": "4", "FieldLabel": "Phone", "FieldType": "TEXT", "CustomeAttributeId": -1, "FixedFieldAttributeId": -1, "DisplayFormat": "", "DefaultValue": "", "IsReadOnly": false, "IsDisplayed": true, "IsMandatory": true, "IsUnique": true, "IsCompositeUnique": false, "MinLength": -1, "MaxLength": -1, "SpecificLength": "-1", "ValidationType": "REGEX", "RegexValue": "^[0-9]+$", "CustomerFieldValueList": []}, {"FieldName": "EMAIL", "Description": "Show EMAIL", "EntityFieldName": "Attribute1", "Primarygroup": "Contact", "SubGroup": "Email", "FieldOrder": "5", "FieldLabel": "Email", "FieldType": "TEXT", "CustomeAttributeId": -1, "FixedFieldAttributeId": -1, "DisplayFormat": "", "DefaultValue": "", "IsReadOnly": false, "IsDisplayed": true, "IsMandatory": false, "IsUnique": false, "IsCompositeUnique": false, "MinLength": -1, "MaxLength": -1, "SpecificLength": "-1", "ValidationType": "REGEX", "RegexValue": "^([\\w\\.\\-]+)@([\\w\\-]+)((\\.(\\w){2,})+)$", "CustomerFieldValueList": []}, {"FieldName": "MEMBERSHIP_ID", "Description": "Show Membership Name", "EntityFieldName": "MembershipId", "Primarygroup": "Primary", "SubGroup": "", "FieldOrder": "", "FieldLabel": "Membership", "FieldType": "OBJECT", "CustomeAttributeId": -1, "FixedFieldAttributeId": -1, "DisplayFormat": "", "DefaultValue": "", "IsReadOnly": true, "IsDisplayed": true, "IsMandatory": false, "IsUnique": false, "IsCompositeUnique": false, "MinLength": -1, "MaxLength": -1, "SpecificLength": "", "ValidationType": "NONE", "RegexValue": "", "CustomerFieldValueList": null}, {"FieldName": "VERIFIED", "Description": "Verified", "EntityFieldName": "Verified", "Primarygroup": "Primary", "SubGroup": "", "FieldOrder": "", "FieldLabel": "Verified", "FieldType": "FLAG", "CustomeAttributeId": -1, "FixedFieldAttributeId": -1, "DisplayFormat": "", "DefaultValue": "", "IsReadOnly": true, "IsDisplayed": true, "IsMandatory": false, "IsUnique": false, "IsCompositeUnique": false, "MinLength": -1, "MaxLength": -1, "SpecificLength": "", "ValidationType": "NONE", "RegexValue": "", "CustomerFieldValueList": null}], "Hash": "6DEFB0DE44EFA4421DEB251DDB80DE53"}}