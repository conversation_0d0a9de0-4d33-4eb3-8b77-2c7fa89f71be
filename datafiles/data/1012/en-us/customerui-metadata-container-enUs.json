{"data": {"CustomerUIMetadataContainerDTOList": [{"CustomerFieldOrder": 1, "CustomAttributeFlag": 0, "CustomAttributeId": -1, "CustomerFieldName": "CUSTOMER_NAME", "EntityFieldCaption": "First Name", "CustomerFieldValues": null, "EntityFieldName": "ProfileDTO.FirstName", "CustomerFieldValue": "", "CustomerFieldType": "TEXT", "ValidationType": "M", "FieldLength": "999", "DisplayFormat": null, "CustomAttributesContainerDTO": {"CustomAttributeId": -1, "Name": null, "Sequence": 0, "Type": null, "Applicability": null, "Access": null, "CustomAttributeValueListContainerDTOList": []}}, {"CustomerFieldOrder": 2, "CustomAttributeFlag": 0, "CustomAttributeId": -1, "CustomerFieldName": "LAST_NAME", "EntityFieldCaption": "Last Name", "CustomerFieldValues": null, "EntityFieldName": "ProfileDTO.LastName", "CustomerFieldValue": "", "CustomerFieldType": "TEXT", "ValidationType": "O", "FieldLength": "999", "DisplayFormat": null, "CustomAttributesContainerDTO": {"CustomAttributeId": -1, "Name": null, "Sequence": 0, "Type": null, "Applicability": null, "Access": null, "CustomAttributeValueListContainerDTOList": []}}, {"CustomerFieldOrder": 3, "CustomAttributeFlag": 0, "CustomAttributeId": -1, "CustomerFieldName": "BIRTH_DATE", "EntityFieldCaption": "Birth Date", "CustomerFieldValues": null, "EntityFieldName": "ProfileDTO.DateOfBirth", "CustomerFieldValue": "", "CustomerFieldType": "DATE", "ValidationType": "O", "FieldLength": "999", "DisplayFormat": null, "CustomAttributesContainerDTO": {"CustomAttributeId": -1, "Name": null, "Sequence": 0, "Type": null, "Applicability": null, "Access": null, "CustomAttributeValueListContainerDTOList": []}}, {"CustomerFieldOrder": 4, "CustomAttributeFlag": 0, "CustomAttributeId": -1, "CustomerFieldName": "CONTACT_PHONE", "EntityFieldCaption": "Phone", "CustomerFieldValues": null, "EntityFieldName": "ProfileDTO.ContactDTOList[0].Attribute1", "CustomerFieldValue": "", "CustomerFieldType": "TEXT", "ValidationType": "M", "FieldLength": "999", "DisplayFormat": null, "CustomAttributesContainerDTO": {"CustomAttributeId": -1, "Name": null, "Sequence": 0, "Type": null, "Applicability": null, "Access": null, "CustomAttributeValueListContainerDTOList": []}}, {"CustomerFieldOrder": 5, "CustomAttributeFlag": 0, "CustomAttributeId": -1, "CustomerFieldName": "PIN", "EntityFieldCaption": "Post Code", "CustomerFieldValues": null, "EntityFieldName": "ProfileDTO.AddressDTOList[0].PostalCode", "CustomerFieldValue": "", "CustomerFieldType": "TEXT", "ValidationType": "O", "FieldLength": "999", "DisplayFormat": null, "CustomAttributesContainerDTO": {"CustomAttributeId": -1, "Name": null, "Sequence": 0, "Type": null, "Applicability": null, "Access": null, "CustomAttributeValueListContainerDTOList": []}}, {"CustomerFieldOrder": 6, "CustomAttributeFlag": 0, "CustomAttributeId": -1, "CustomerFieldName": "EMAIL", "EntityFieldCaption": "Email", "CustomerFieldValues": null, "EntityFieldName": "ProfileDTO.ContactDTOList[0].Attribute1", "CustomerFieldValue": "", "CustomerFieldType": "TEXT", "ValidationType": "M", "FieldLength": "999", "DisplayFormat": null, "CustomAttributesContainerDTO": {"CustomAttributeId": -1, "Name": null, "Sequence": 0, "Type": null, "Applicability": null, "Access": null, "CustomAttributeValueListContainerDTOList": []}}, {"CustomerFieldOrder": 7, "CustomAttributeFlag": 0, "CustomAttributeId": -1, "CustomerFieldName": "CUSTOMER_ID_PROOF", "EntityFieldCaption": "Customer <PERSON><PERSON>of", "CustomerFieldValues": null, "EntityFieldName": "customers.IDProofFileName", "CustomerFieldValue": "", "CustomerFieldType": "TEXT", "ValidationType": "O", "FieldLength": "999", "DisplayFormat": null, "CustomAttributesContainerDTO": {"CustomAttributeId": -1, "Name": null, "Sequence": 0, "Type": null, "Applicability": null, "Access": null, "CustomAttributeValueListContainerDTOList": []}}], "Hash": "DE87FAAB74F92D7F7F0F8C15589AB714"}}