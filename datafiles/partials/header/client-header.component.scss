.header {
    display: flex;
    justify-content: space-around;
    align-items: center;
        background: #1226aa
}

.menu {
    font-family: roboto;
}

.link-item {
    float: left;

    a {
        padding: 20px;
        display: block;
        text-align: center;
    }
}

.menu-icon {
    display: none;
    cursor: pointer;
    padding: 28px 20px;

    .navicon {
        height: 2px;
        background: #333;
    }
}

.menu-btn {
    &:checked {
        ~.menu {
            display: flex !important;
            max-height: 530px;
        }

        ~.menu-icon {
            .navicon {
                background: transparent;

                &::before {
                    transform: rotate(-45deg);
                }

                &::after {
                    transform: rotate(45deg);
                }
            }
        }
    }
}

@media screen and (max-device-width: 1024px) {
    .menu-icon {
        display: block !important;
    }

    nav {
        display: none;
    }

    [type="checkbox"]:checked~nav {
        display: block;
        position: absolute;
        left: 20px;
        right: 20px;
        z-index: 9999;
    }

    .link-item {
        width: 100%;
        background: #222;
        text-indent: 20px;
    }
}