/**
 * @fileoverview Business layer service for handling signed waiver functionality
 * <AUTHOR> Name]
 * @version 1.0.0
 */
import { inject, Injectable, signal } from '@angular/core';
import { SignedWaiverDL } from '../data-layer/signed-waiver-dl.service';
import { RequestState } from 'lib-app-core';
import { CustomerSignedWaiverDTO } from '../../models/customer-signed-waiver-dto.model';

@Injectable()
export class SignedWaiverBL extends RequestState {
    private _signedWaiverDL = inject(SignedWaiverDL);
    signedWaivers = signal<CustomerSignedWaiverDTO[]>([]);
    errorMessage = signal<string | null>(null);

    /**
     * Loads signed waivers for a specific customer by building API parameters,
     * invoking the Data Layer service, and managing the request state.
     *
     * @param customerId - The customer ID to fetch signed waivers for.
     */
    loadSignedWaivers(customerId: string | null) {
        if (!customerId) {
            this.errorMessage.set('Customer ID not found. Please log in again.');
            return;
        }

        this._signedWaiverDL.buildApiParams(customerId);
        const signedWaivers$ = this._signedWaiverDL.load();
        
        this._handleRequest(signedWaivers$, (response) => {
            this.signedWaivers.set(response.data || []);
            this.errorMessage.set(null);
        }, (error) => {
            this.errorMessage.set('Failed to load signed waivers.');
        });
    }
} 