/**
 * @fileoverview Data layer service for handling signed waiver API calls
 * <AUTHOR> Name]
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import { CustomerSignedWaiverResponse } from '../../models/customer-signed-waiver-dto.model';

@Injectable()
export class SignedWaiverDL extends ApiServiceBase {
    private _customerId: string = '';

    constructor() {
        // Call the parent constructor with the specific API endpoint and action type
        super('signed_waivers_data', 'GET_SIGNED_WAIVERS');
        this.init();
    }

    // Builds the API parameters for the signed waivers request
    buildApiParams(customerId: string) {
        this._customerId = customerId;
    }

    // Returns the API URL for the signed waivers request
    load(): Observable<CustomerSignedWaiverResponse> {
        const url = `${this.getApiUrl()}?customerId=${this._customerId}`;
        return this._http.get<CustomerSignedWaiverResponse>(url);
    }
} 